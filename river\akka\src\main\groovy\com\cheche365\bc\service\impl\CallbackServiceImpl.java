package com.cheche365.bc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.cheche365.bc.service.CallbackService;
import com.cheche365.bc.task.AutoTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class CallbackServiceImpl implements CallbackService {

    private static final String CALLBACK_SUCCESS = "回写成功";

    private static final String[] REMOVE_KEYS = new String[]{"bsNotificationDescription", "bzNotificationDescription", "specialAgreement"};


    @Override
    public void handle(AutoTask autoTask, String result) {

        //任务结果回写
        JSONObject resultJson = JSONObject.parseObject(result);

        if (!CALLBACK_SUCCESS.equals(resultJson.getString("msg"))) {
            log.error("任务:{}回写结果异常, 回写url:{}, 回写json:{}, 回写结果:{}", autoTask.getTaskId(), autoTask.getCallBackUrl(), autoTask.getFeedbackJson(), result);
        } else {
            log.info("任务:{}回写返回结果{}", autoTask.getTaskId(), result);
        }

        //补充商业险与交强险的投保单号和订单号
        setProposeNoAndOrderNo(autoTask);

        //补充"平台信息"与"回写结果"到备注
        setResultStr(autoTask, result);

        //过滤长文本
        filterLongText(autoTask);
    }


    private void filterLongText(AutoTask autoTask) {
        Map map = JSON.parseObject(autoTask.getApplyJson(), Map.class);

        try {
            for (String key : REMOVE_KEYS) {
                JSONPath.remove(map, "$.sq.misc." + key);
            }
        } catch (Exception e) {
            log.warn("特约和浮动告知单日志过滤异常:{}", ExceptionUtils.getStackTrace(e));
        }

        autoTask.setApplyJson(JSON.toJSONString(map));
    }

    private void setResultStr(AutoTask autoTask, String result) {
        String platformInfo = autoTask.getPlatformInfo();
        //taskId是PlatformUtil初始化平台信息参数时初始化的，以此为依据判断是否回写平台信息
        if (platformInfo.contains("taskId")) {
            autoTask.setResultStr(String.format("回写的平台信息为：%s\n回写返回结果:%s,%s", platformInfo, result, autoTask.getIsReserved()));
        } else {
            autoTask.setResultStr(String.format("回写返回结果:%s", result));
        }
    }

    /**
     * EDI 会保存投保单号和订单号
     */
    private void setProposeNoAndOrderNo(AutoTask autoTask) {
        String taskType = autoTask.getTaskType().toLowerCase();
        if (taskType.startsWith("edi") && (taskType.contains("autoinsure") || taskType.contains("insure"))) {
            var taskEntity = autoTask.getTaskEntity();

            autoTask.setBizProposeNo((String) JSONPath.eval(taskEntity, "$.enquiry.SQ.bizProposeNum"));
            autoTask.setEfcProposeNo((String) JSONPath.eval(taskEntity, "$enquiry.SQ.efcProposeNum"));

            String orderNo = (String) JSONPath.eval(taskEntity, "$.enquiry.SQ.orderNo");
            if (Objects.nonNull(orderNo)) {
                autoTask.setBizPolicyNo(orderNo);
            }
        }
    }
}
