package com.cheche365.bc.actor.callable;

import akka.actor.ActorRef;
import akka.dispatch.OnComplete;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.entity.mysql.PolicySource;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.exception.TaskException;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.PolicySourceService;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.tools.FileUtil;
import com.cheche365.bc.utils.DataUtil;
import com.cheche365.bc.utils.ErrorInfoUtil;
import com.cheche365.bc.utils.sdas.SDASUtils;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.HttpClients;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.cheche365.bc.constants.Constants.*;


@Slf4j
@RequiredArgsConstructor
@Builder
public class TaskOnComplete extends OnComplete<AutoTask> {

    // 处理类型枚举
    private final HandlerType handlerType;

    private final AutoTask autoTask;

    private final ActorRef actorRef;

    private final InterfaceService interfaceService;

    private final PolicySourceService policySourceService;

    public enum HandlerType {
        REQUEST,
        PROCESS,
        CALLBACK
    }

    @Override
    public void onComplete(Throwable failure, AutoTask success) throws Throwable, Throwable {
        if (failure != null) {
            handleFailure(failure);
        } else {
            handleSuccess(success);
        }
    }

    private void handleSuccess(AutoTask success) {
        switch (handlerType) {
            case CALLBACK:
                log.info("跟踪号:{},公司:{},接口调用流程执行完成!", success.getTraceKey(), success.getCompanyId());
                break;
            case PROCESS:
                handleProcessSuccess(success);
                break;
            case REQUEST:
                handleRequestSuccess(success);
                break;
        }
    }

    private void handleFailure(Throwable failure) {
        if (failure instanceof TaskException taskException) {
            AutoTask task = taskException.getTask();

            // 通用失败处理
            task.setEndFlag(true);
            task.setResultFlag(false);

            switch (handlerType) {
                case CALLBACK:
                    handleCallbackFailure(task, failure);
                    break;
                case PROCESS:
                    handleProcessFailure(task, failure);
                    break;
                case REQUEST:
                    task.setResultStr(failure.getMessage());
                    break;
            }
            if (handlerType != HandlerType.CALLBACK) {
                actorRef.tell(task, ActorRef.noSender());
            }
        } else {
            if (handlerType == HandlerType.CALLBACK) {
                log.error("跟踪号:{},公司:{},接口调用流程执行完成!出现未知异常{}", autoTask.getTraceKey(), autoTask.getCompanyId(), ExceptionUtils.getStackTrace(failure));
            }
        }
    }

    private void handleProcessFailure(AutoTask task, Throwable failure) {
        Interface anInterface = interfaceService.getInterfaceByTaskType(task.getTaskType());
        String errorMessage = failure.getMessage();
        if (Strings.isNullOrEmpty(task.getTaskStatus())) {
            task.setTaskStatus(anInterface.getDefaultFailedStatus());
        }
        log.error("任务:{}接口调用流程发生异常！", task.getTaskId());
        if (!task.getErrorInfo().containsKey(ErrorInfoKeys.ERROR_CODE)) {
            task.getErrorInfo().putAll(ErrorInfoUtil.build(ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde(), errorMessage));
        }
        task.setResultStr(errorMessage);
        task.setFailureCause(errorMessage);
    }

    private void handleCallbackFailure(AutoTask task, Throwable failure) {
        Interface anInterface = interfaceService.getInterfaceByTaskType(task.getTaskType());
        task.setTaskStatus(anInterface.getDefaultFailedStatus());
        task.setResultStr(failure.getMessage());
        autoTask.getErrorInfo().putAll(ErrorInfoUtil.build(ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde(), failure.getMessage()));
        log.error("跟踪号:{},公司:{},接口回调流程发生异常{}", autoTask.getTraceKey(), autoTask.getCompanyId(), ExceptionUtils.getStackTrace(failure));
    }


    private void handleRequestSuccess(AutoTask success) {
        log.info("任务:{}申请任务数据成功！,数据转换成功！", success.getTaskId());
        if (success.getTaskType().contains("policyDownload")) {
            //如果是电子保单下载，验证是否已下载过
            policyDownloadCheck(success);
        }
        actorRef.tell(success, ActorRef.noSender());
    }

    private void handleProcessSuccess(AutoTask success) {
        success.setResultFlag(true);
        success.setEndFlag(true);
        Interface anInterface = interfaceService.getInterfaceByTaskType(success.getTaskType());
        if (Strings.isNullOrEmpty(success.getTaskStatus()) || !TaskStatus.isCallbackStatus(success.getTaskStatus())) {
            if (!DataUtil.containsKey((Map) success.getTaskEntity(), "enquiry.taskStatus")) {
                success.setTaskStatus(anInterface.getDefaultSuccessStatus());
            } else {
                success.setTaskStatus((String) DataUtil.get("enquiry.taskStatus", success.getTaskEntity()));
            }
        }
        log.info("任务:{}接口调用流程执行完成,结果状态:{}!", String.format("%s@%s", success.getTraceKey(), success.getCompanyId()), success.getTaskStatus());
        actorRef.tell(success, ActorRef.noSender());
    }

    /**
     * 电子保单下载验证：1.判断是否已下载 2。已下载：将任务结束标志置为true不再与保司交互。 未下载：继续执行流程
     */
    private void policyDownloadCheck(AutoTask autoTask) {
        String applyJsonStr = autoTask.getApplyJson();
        JSONObject applyJson = JSON.parseObject(applyJsonStr);
        JSONObject sq = applyJson.getJSONObject("sq");

        if (Objects.isNull(sq) || sq.isEmpty()) {
            terminateTask(autoTask, "电子保单下载业务数据不全");
            return;
        }

        Map<String, String> sourceMap = buildPolicyNoMap(autoTask, sq);
        if (CollUtil.isEmpty(sourceMap)) {
            return;
        }

        Map<String, String> policyNoMap = reversedPolicyNoMap(sourceMap);

        //查询数据资产
        List<PolicySource> policySourceList = policySourceService.listByPolicyNos(policyNoMap.keySet());
        if (CollUtil.isEmpty(policySourceList)) return;

        if (autoTask.getConfigs().containsKey("policyEmail")) {
            decryptEPolicy(policySourceList, autoTask, policyNoMap);
        }

        var tempValues = autoTask.getTempValues();
        policySourceList.stream().filter(policySource -> StrUtil.isNotBlank(policySource.getSourceId()))
                .forEach(policySource -> {
                    String tempValuesKey = policyNoMap.remove(policySource.getPolicyNo());
                    tempValues.put(tempValuesKey, policySource.getSourceId());
                });

        //是否已全部下载完成
        if (policyNoMap.isEmpty()) {
            autoTask.setTaskStatus(TaskStatus.EXECUTE_SUCCESS.getState());
            if (isNotPicc(autoTask)) {
                autoTask.setEndFlag(true);
            }
        } else if (autoTask.getConfigs().containsKey("policyEmail") && isNotPicc(autoTask)) {
            //TODO 应该有一个统一判定
            if (!policySourceList.isEmpty()) {
                autoTask.setTaskStatus(TaskStatus.EXECUTE_SUCCESS.getState());
                autoTask.setEndFlag(true);
            } else {
                noFoundPolicyFromEmail(autoTask);
            }
        }
    }

    private static Map<String, String> reversedPolicyNoMap(Map<String, String> policyNoMap) {
        return policyNoMap.entrySet().stream()
                .filter(entry -> StrUtil.isNotBlank(entry.getValue()))
                .peek(entry -> entry.setValue(StrUtil.reverse(entry.getValue())))
                .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));
    }

    private static Map<String, String> buildPolicyNoMap(AutoTask autoTask, JSONObject sq) {
        Map<String, String> policyNoMap = Maps.newHashMapWithExpectedSize(3);
        //获取交强险保单号
        String efcPolicyNo = sq.getString("efcPolicyNo");
        if (StringUtils.isBlank(efcPolicyNo)) {
            efcPolicyNo = sq.getString("efcPolicyCode");
        }

        //获取商业险保单号
        String bizPolicyNo = sq.getString("bizPolicyNo");
        if (StringUtils.isBlank(bizPolicyNo)) {
            bizPolicyNo = sq.getString("bizPolicyCode");
        }

        //获取非车险保单号
        JSONObject nonMotor = sq.getJSONObject("nonMotor");
        String nonMotorPolicyNo = null;
        if (Objects.nonNull(nonMotor)) {
            nonMotorPolicyNo = nonMotor.getString("accidentPolicyCode");
            if (StringUtils.isBlank(nonMotorPolicyNo)) {
                nonMotorPolicyNo = nonMotor.getString("accidentProposeCode");
            }
        }

        //判断是否有保单号
        if (StrUtil.isAllBlank(efcPolicyNo, bizPolicyNo, nonMotorPolicyNo)) {
            String errorInfo = String.format("任务:%s申请任务数据,申请返回内容中保单号为空！", autoTask.getTraceKey().concat("@").concat(autoTask.getCompanyId()));
            terminateTask(autoTask, errorInfo);
            return policyNoMap;
        }

        policyNoMap.put(POLICY_SOURCE_EFC, efcPolicyNo);
        policyNoMap.put(Constants.POLICY_SOURCE_BIZ, bizPolicyNo);
        policyNoMap.put(POLICY_SOURCE_NON_MOTOR, nonMotorPolicyNo);

        return policyNoMap;
    }

    private void decryptEPolicy(List<PolicySource> policySourceList, AutoTask autoTask, Map<String, String> policyNoMap) {
        policySourceList.forEach(source -> {
                if (StringUtils.isBlank(source.getSourceId()) && StringUtils.isNotBlank(source.getZipSourceId())) {
                    String applyJsonStr = autoTask.getApplyJson();
                    JSONObject applyJson = JSON.parseObject(applyJsonStr);
                    JSONObject insureInfoJson = applyJson.getJSONObject("applicantPersonInfo");
                    String applicantIdCard = insureInfoJson.getString("idCard");
                    if (StringUtils.isNotBlank(applicantIdCard) && applicantIdCard.length() > 5) {
                        // 人保投保人证件号码后6位作为解压密码，若为企业客户且没有统一社会信用代码，则为888888，太保为投保人证件号码后4为，
                        String unZipPassword = applicantIdCard.substring(applicantIdCard.length() - 6);
                        List<String> passwords = List.of(unZipPassword, unZipPassword.substring(2, 6), "888888");
                        passwords.forEach(password -> {
                            if (StringUtils.isBlank(source.getSourceId())) {
                                unZipAndGetSourceId(source, password);
                            }
                        });

                    }
                }
                if (Objects.nonNull(source.getSourceId()) && Objects.isNull(source.getPolicyType())) {
                    // 定时任务从邮箱下载的保单只存了保单号，此时要初始化保单类型，保司信息, 避免重复更新
                    source.setPolicyType(convertPolicyType(policyNoMap.get(source.getPolicyNo())));
                    source.setInsId(Integer.valueOf(autoTask.getCompanyId().substring(0, 4)));
                    policySourceService.updateById(source);
                }
            }
        );
    }

    /**
     * 特殊保司 zip包需要解密 目前只有人保
     *
     * @param source
     * @param password
     */
    private void unZipAndGetSourceId(PolicySource source, String password) {

        try {
            String zipSourceId = source.getZipSourceId();
            String location = SDASUtils.getLocationById(zipSourceId);
            if (StringUtils.isNotBlank(location)) {
                Object fileBytes = HttpSender.doGetWithRetry(1, HttpClients.createDefault(), location, null, "UTF-8", null, true);
                if (Objects.nonNull(fileBytes)) {
                    // 原始的zip文件用密码解压，获取pdf文件，上传obs获取pdf的sourceId
                    String fileName = new StringBuilder(source.getPolicyNo()).reverse() + ".pdf";
                    byte[] policyFileBytes = FileUtil.getStreamAsByteArray((byte[]) fileBytes, password, fileName);
                    if (Objects.nonNull(policyFileBytes) && policyFileBytes.length > 0) {
                        String path = Path.of(OBS_POLICY_PATH_PREFIX, "pdf", String.valueOf(source.getInsId()), fileName).toString();
                        String sourceId = SDASUtils.uploadOBS(fileName, policyFileBytes, path);
                        if (StringUtils.isNotBlank(sourceId)) {
                            source.setSourceId(sourceId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("zip保单上传OBS获取pdf资源id异常: {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private static void terminateTask(AutoTask autoTask, String errorInfo) {
        autoTask.setEndFlag(true);
        autoTask.setResultFlag(false);

        autoTask.setResultStr(errorInfo);
        autoTask.setTaskStatus(TaskStatus.EXECUTE_FAILURE.getState());
        autoTask.getErrorInfo().putAll(ErrorInfoUtil.build(ExceptionCde.DATA_DEFECT.getCde(), errorInfo));
        autoTask.setFailureCause(errorInfo);
        log.error(errorInfo);
    }

    private static void noFoundPolicyFromEmail(AutoTask task) {
        String errorInfo = "电子保单下载失败";
        task.setEndFlag(true);
        task.setResultFlag(false);
        task.setTaskStatus(TaskStatus.EXECUTE_FAILURE.getState());
        task.getErrorInfo().putAll(ErrorInfoUtil.build(ExceptionCde.DATA_DEFECT.getCde(), errorInfo));
        task.setResultStr(errorInfo);
        task.setFailureCause(errorInfo);
    }

    // 需求14499
    private static boolean isNotPicc(AutoTask autoTask) {
        boolean isOnline = MapUtils.getBoolean(autoTask.getConfigs(), "isOnline", false);
        boolean isPicc = autoTask.getCompanyId().startsWith(String.valueOf(InsCompanyEnum.PICC.getCode()));
        return isOnline || !isPicc;
    }
}
