package com.cheche365.bc.utils;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.config.SpringUtil;
import com.cheche365.bc.constants.Constants;
import com.cheche365.bc.constants.PlatformConstants;
import com.cheche365.bc.constants.TaskEntryKey;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.model.*;
import com.cheche365.bc.model.car.*;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.task.KeepSessionConfig;
import com.cheche365.bc.tools.DateCalcUtil;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.cheche365.bc.utils.sender.HttpSender.buildHttpClient;

/**
 * 任务工具
 *
 * <AUTHOR>
 * @Created by austinChen on 2016/12/13 13:22.
 */
@Component
@Slf4j
public final class TaskUtil implements InitializingBean {

    private static Map<String, String> carPriceTypeMap = new HashMap<>();
    public static Map<String, BaseHandler> requestMethodMap = Maps.newConcurrentMap();


    @Override
    public void afterPropertiesSet() {
        carPriceTypeMap.put(CarPriceType.NO_TAX_PRICE, "1");
        carPriceTypeMap.put(CarPriceType.NO_TAX_PRICE_COMP, "2");
        carPriceTypeMap.put(CarPriceType.TAX_PRICE, "3");
        carPriceTypeMap.put(CarPriceType.TAX_PRICE_COMP, "4");
        SpringUtil.getApplicationContext().getBeansOfType(BaseHandler.class).values().forEach(
            handler -> requestMethodMap.put(handler.getMethodType(), handler)
        );
    }

    public static RequestConfig buildReqConfig(AutoTask task) {
        RequestConfig requestConfig = null;
        Map configMap = task.getConfigs();
        if (configMap != null) {
            int socketTimeout = 30000;
            int connectTimeout = 15000;
            int connectionRequestTimeout = 15000;
            if (configMap.containsKey("timeoutConfig")) {
                String timeoutConfig = (String) configMap.get("timeoutConfig");
                JSONObject timeoutConfigJson = JSONObject.parseObject(timeoutConfig);
                if (timeoutConfigJson.containsKey("socketTimeout")) {
                    socketTimeout = timeoutConfigJson.getIntValue("socketTimeout");
                }
                if (timeoutConfigJson.containsKey("connectTimeout")) {
                    connectTimeout = timeoutConfigJson.getIntValue("connectTimeout");
                }
                if (timeoutConfigJson.containsKey("connectionRequestTimeout")) {
                    connectionRequestTimeout = timeoutConfigJson.getIntValue("connectionRequestTimeout");
                }
            }
            requestConfig = RequestConfig.custom()
                .setSocketTimeout(socketTimeout)
                .setConnectTimeout(connectTimeout)
                .setConnectionRequestTimeout(connectionRequestTimeout)
                .setStaleConnectionCheckEnabled(true)
                .build();
            if (configMap.containsKey(HttpSender.proxyHost)
                && configMap.containsKey(HttpSender.proxyPort)
                && configMap.containsKey(HttpSender.proxyName)
                && configMap.containsKey(HttpSender.proxyPass)) {
                requestConfig = RequestConfig.copy(requestConfig).setProxy(new HttpHost(configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()))).build();
            }

        }
        if (requestConfig == null) {
            requestConfig = HttpSender.getDefaultRequestConfig();
        }
        return requestConfig;
    }


    public static Enquiry transform(JSONObject enquiryJsonObject, String taskType) {
        Enquiry enquiry = new Enquiry();

        Order order = initOrder(enquiryJsonObject, enquiry);
        enquiry.setOrder(order);

        //续期处理
        transformRenewalInfo(enquiry, enquiryJsonObject, taskType);

        //初始化 misc
        Map misc = MapUtil.isNotEmpty(enquiry.getMisc()) ? enquiry.getMisc() : Maps.newHashMap();
        enquiry.setMisc(misc);

        //转保验证码
        Map<String, String> scr = Maps.newHashMap();
        scr.put("answerFlag", "2");
        enquiry.setCheckCode(scr);

        //去年信息
        LastYearInfo lastYearInfo = new LastYearInfo();
        order.setLastYearInfo(lastYearInfo);

        //转换sq信息中需查询的投保单号
        if (enquiryJsonObject.containsKey("sq")) {
            JSONObject sq = enquiryJsonObject.getJSONObject("sq");
            //投保单号
            setValue(sq, "efcProposeNum", enquiry::setEfcProposeNum);
            setValue(sq, "bizProposeNum", enquiry::setBizProposeNum);
            //保单号
            setValue(sq, "efcPolicyCode", enquiry::setEfcPolicyCode);
            setValue(sq, "bizPolicyCode", enquiry::setBizPolicyCode);
            //上年保单信息
            setValue(sq, "sypolicyno", lastYearInfo::setLastBizPolicyCode);
            setValue(sq, "jqpolicyno", lastYearInfo::setLastEfcPolicyCode);
            setValue(sq, "supplierid", lastYearInfo::setLastComId);

            //机构编码
            if (sq.containsKey("deptCode")) {
                enquiry.setDeptCode(sq.getString("deptCode"));
            }

            //转保
            if (sq.containsKey("compulsoryCheckCode")) {
                scr.put("isTrafficQuestion", "Y");
                scr.put("questionAnswer", sq.getString("compulsoryCheckCode"));
            }
            if (sq.containsKey("commercialCheckCode")) {
                scr.put("isTrafficQuestion", "N");
                scr.put("questionAnswer", sq.getString("commercialCheckCode"));
            }

            //驾意险
            if (sq.containsKey(Constants.NO_MOTOR)) {
                misc.put(Constants.NO_MOTOR, sq.get(Constants.NO_MOTOR));
            }

            //转换sq信息中需查询的上年保单号
            if (sq.containsKey("sypolicyno")) {
                String sypolicyno = sq.getString("sypolicyno");
                if (StringUtil.isNoEmpty(sypolicyno)) {
                    misc.put("sypolicyno", sypolicyno);
                }
            }
            if (sq.containsKey("jqpolicyno")) {
                String jqpolicyno = sq.getString("jqpolicyno");
                if (StringUtil.isNoEmpty(jqpolicyno)) {
                    misc.put("jqpolicyno", jqpolicyno);
                }
            }
        }

        //转换providerInfo信息
        initProviderInfo(enquiryJsonObject, enquiry);

        //特约转换
        transformSpecialClauseMap(enquiryJsonObject, enquiry);

        //添加补充数据项转换；
        initSupplyParamMap(enquiry, enquiryJsonObject);//todo: 重复调用

        //影像资料地址
        initImgAddress(enquiry, enquiryJsonObject);

        return enquiry;
    }

    private static void initProviderInfo(JSONObject enquiryJsonObject, Enquiry enquiry) {
        JSONArray providersInfo = enquiryJsonObject.getJSONArray("providerInfoList");
        if (CollUtil.isNotEmpty(providersInfo)) {
            enquiry.setProviderInfo(JSONObject.toJavaObject((JSONObject) providersInfo.get(0), ProviderInfo.class));
        }
    }

    private static Order initOrder(JSONObject enquiryJsonObject, Enquiry enquiry) {
        Order order = new Order();

        //转换carInfo车辆信息
        transformCarInfo(enquiryJsonObject, order);

        //转换agentInfo代理人信息
        jsonToJavaObject(enquiryJsonObject, "agentInfo", AgentInfo.class, order::setAgentInfo);

        //转换applicantPersonInfo投保人信息
        jsonToJavaObject(enquiryJsonObject, "applicantPersonInfo", InsurePerson.class, order::setInsurePerson);

        //转换insuredPersonInfoList被保人信息
        jsonArrayToJavaObjectList(enquiryJsonObject, "insuredPersonInfoList", InsurePerson.class, order::setInsuredPersons);

        //转换baseSuiteInfo投保保障项信息
        transformBaseSuite(enquiryJsonObject, order);

        //转换beneficiaryPersonList受益人信息
        jsonArrayToJavaObjectList(enquiryJsonObject, "beneficiaryPersonList", BeneficiaryPerson.class, order::setBeneficiaryPersons);

        //转换carOwnerInfo车主信息
        jsonToJavaObject(enquiryJsonObject, "carOwnerInfo", CarOwnerInfo.class, order::setCarOwnerInfo);

        //转换configInfo配置信息
        jsonToJavaObject(enquiryJsonObject, "configInfo", Map.class, enquiry::setConfig);

        //转换非车险种信息
        jsonToJavaObject(enquiryJsonObject, "extSuiteInfo", Map.class, order::setExtSuiteInfo);

        //转换deliverInfo配送信息
        jsonToJavaObject(enquiryJsonObject, "deliverInfo", DeliverInfo.class, order::setDeliverInfo);

        //转换insArea投保地区信息
        jsonToJavaObject(enquiryJsonObject, "insArea", InsArea.class, order::setInsureArea);

        //转换orgCode
        if (null == order.getBelongOrg()) {
            order.setBelongOrg(new IssueInfo());
        }
        order.getBelongOrg().setOrgCode(enquiryJsonObject.getString("orgCode"));
        return order;
    }

    private static void transformBaseSuite(JSONObject enquiryJsonObject, Order order) {

        //转换baseSuiteInfo
        if (!enquiryJsonObject.containsKey("baseSuiteInfo")) return;
        JSONObject baseSuiteInfoJson = enquiryJsonObject.getJSONObject("baseSuiteInfo");
        if (ObjUtil.isEmpty(baseSuiteInfoJson)) return;

        //商业险信息转换
        if (baseSuiteInfoJson.containsKey("bizSuiteInfo")) {
            JSONObject bizSuiteInfo = baseSuiteInfoJson.getJSONObject("bizSuiteInfo");
            JSONArray suits = bizSuiteInfo.getJSONArray("suites");

            Map<String, Object> bizSuits = new HashMap<>();
            for (int i = 0; i < suits.size(); i++) {
                JSONObject suit = suits.getJSONObject(i);
                bizSuits.put(suit.getString("code"), suit);
            }

            bizSuiteInfo.put("suites", bizSuits);
        }

        //车船税信息转换
        if (baseSuiteInfoJson.containsKey("taxSuiteInfo")) {
            JSONObject taxSuite = baseSuiteInfoJson.getJSONObject("taxSuiteInfo");

            String orgCharge = taxSuite.getString("charge");
            if (StrUtil.isNotBlank(orgCharge)) {
                TaxSuiteInfo taxSuiteInfo = new TaxSuiteInfo();
                taxSuiteInfo.setDiscountCharge(new BigDecimal(orgCharge).setScale(2, RoundingMode.HALF_UP));
                baseSuiteInfoJson.put("taxSuiteInfo", taxSuiteInfo);
            }
        }

        order.setSuiteInfo(JSONObject.toJavaObject(baseSuiteInfoJson, BaseSuiteInfo.class));
    }

    private static void transformSpecialClauseMap(JSONObject enquiryJsonObject, Enquiry enquiry) {
        //特约条款
        Map<String, SpecialClause> specialClauseMap = Optional.ofNullable(enquiry.getSpecialClauseMap()).orElseGet(() -> {
            Map<String, SpecialClause> result = new HashMap<>();
            enquiry.setSpecialClauseMap(result);
            return result;
        });

        //特约字符串
        String specialStr = enquiryJsonObject.getString("specialStr");

        if (StrUtil.isBlank(specialStr)) return;

        //特约字符串转换
        Arrays.stream(specialStr.split("@#@"))
            .filter(StringUtils::isNotBlank)
            .map(s -> s.replace("\\|", " \\|") + " ")
            .map(TaskUtil::processSpecialClause)
            .forEach(sc -> specialClauseMap.put(StrUtil.isNotBlank(sc.getCode()) ? sc.getCode() : sc.getDes(), sc));
    }

    /**
     * 处理单条特约条款
     *
     * @param clauseStr 特约条款字符串
     */
    private static SpecialClause processSpecialClause(String clauseStr) {
        SpecialClause specialClause = new SpecialClause();
        if (!clauseStr.endsWith("|")) {
            clauseStr = clauseStr + "|";
        }
        try {
            String reg = "(?<type>\\d)\\|(?<code>\\w+)\\|(?<title>.*?)(?:\\|(?<content>.*?))?\\|";
            Pattern pattern = Pattern.compile(reg);
            Matcher matcher = pattern.matcher(clauseStr);
            if (matcher.find()) {
                String type = matcher.group("type");
                String code = matcher.group("code");
                String title = matcher.group("title");
                String content = matcher.group("content");
                specialClause.setFlag(type);
                specialClause.setCode(code);
                specialClause.setDes(title);
                specialClause.setClauses(content);
            }
        } catch (Exception e) {
            log.error("特约初始化异常： {}", ExceptionUtils.getStackTrace(e));
        }
        return specialClause;
    }

    public static Enquiry transform(String taskEntityStr, String taskType) {
        if (log.isDebugEnabled()) {
            log.debug("申请到的任务数据{}", taskEntityStr);
        }
        JSONObject enquiryJsonObject = JSONObject.parseObject(taskEntityStr);
        return transform(enquiryJsonObject, taskType);
    }

    /**
     * carLicense 车牌
     * carOwner 车主姓名
     * carVin 车辆识别代号
     * carEngineNum 发动机号
     * insuredName 被保人名称
     * carLicenseType 号牌种类
     * carFirstRegDate 初登日期
     * carOwnerIdType 车主证件类型
     * carOwnerIdNum 车主证件号码
     * insuredIdNum 被保人身份证号码
     * lastCommercialPoliceyNum 上年商业险投保单号
     *
     * @param enquiry
     * @param enquiryJsonObject
     */
    //续保信息补充
    private static void transformRenewalInfo(Enquiry enquiry, JSONObject enquiryJsonObject, String taskType) {
        String isRenewal = enquiryJsonObject.getString("isrenewal");
        boolean renewalFlag = "true".equals(isRenewal);
        //转换续保标识
        enquiry.setRenewal(renewalFlag);
        if (!renewalFlag) {
            return;
        }
        JSONArray renewalInfo = enquiryJsonObject.getJSONArray("renewalquoteitem");

        Map<String, String> renewalquoteitemMap = new HashMap<>();
        if (CollUtil.isEmpty(renewalInfo)) {
            return;
        }
        for (int i = 0; i < renewalInfo.size(); i++) {
            JSONObject item = renewalInfo.getJSONObject(i);
            renewalquoteitemMap.put(item.getString("itemcode"), item.getString("itemvalue"));
        }

        String commercialInsSameFlag = enquiryJsonObject.getJSONObject("carInfo").getString("insureconfigsameaslastyear");
        if (StrUtil.isNotBlank(commercialInsSameFlag)) {
            renewalquoteitemMap.put("renewalSameFlag", commercialInsSameFlag);
        }
        if (enquiry.getMisc() == null) {
            enquiry.setMisc(new HashMap());
        }
        enquiry.getMisc().putAll(renewalquoteitemMap);//续保数据并不包含人车险信息，查询流程时初始化上述对象

        //订单
        Order order = Optional.ofNullable(enquiry.getOrder()).orElseGet(() -> {
            Order o = new Order();
            enquiry.setOrder(o);
            return o;
        });

        //投保人
        order.setInsurePerson(Optional.ofNullable(order.getInsurePerson()).orElseGet(InsurePerson::new));

        //被保人
        order.setInsuredPersons(Optional.ofNullable(order.getInsuredPersons()).orElseGet(() -> {
            List<InsurePerson> insuredPersons = Lists.newArrayList();
            insuredPersons.add(new InsurePerson());
            return insuredPersons;
        }));

        //车主
        order.setCarOwnerInfo(Optional.ofNullable(order.getCarOwnerInfo()).orElseGet(CarOwnerInfo::new));

        //权益人
        order.setBeneficiaryPersons(Optional.ofNullable(order.getBeneficiaryPersons()).orElseGet(() -> {
            List<BeneficiaryPerson> insuredPersons = Lists.newArrayList();
            insuredPersons.add(new BeneficiaryPerson());
            return insuredPersons;
        }));

        //车辆信息
        order.setCarInfo(Optional.ofNullable(order.getCarInfo()).orElseGet(CarInfo::new));

        //险种主对象
        order.setSuiteInfo(Optional.ofNullable(order.getSuiteInfo()).orElseGet(BaseSuiteInfo::new));
    }

    //车辆信息转换补充
    private static void transformCarInfo(JSONObject enquiryJsonObject, Order order) {
        JSONObject carInfoJson = enquiryJsonObject.getJSONObject("carInfo");
        carInfoJson.remove("misc");
        CarInfo carInfo = JSONObject.toJavaObject(carInfoJson, CarInfo.class);
        //指定车价处理，选择默认为含税价
        String carPriceType = CarPriceType.TAX_PRICE;
        if (enquiryJsonObject.containsKey(CarPriceType.CAR_PRICE_TYPE_CODE)) {
            carPriceType = enquiryJsonObject.getString(CarPriceType.CAR_PRICE_TYPE_CODE);
        }
        carInfo.getMisc().put(CarPriceType.CAR_PRICE_TYPE_CODE, carPriceTypeMap.get(carPriceType));
        try {
            String vin = carInfo.getVin();
            if (StringUtil.isNoEmpty(vin) && vin.length() >= 17 && (vin.startsWith("L") || vin.startsWith("l"))) {
                //国产类型
                carInfo.setGlassType(GlassType.Domestic.ordinal());
            } else {
                //进口类型
                carInfo.setGlassType(GlassType.importation.ordinal());
            }
        } catch (Exception e) {
            log.error("车型玻璃类型转换错误");
        }
        order.setCarInfo(carInfo);
    }

    /**
     * 获取投保单号
     *
     * @param enquiryJsonObject JSON对象
     * @param key               要获取的key: efcProposeNum(交强险) 或 bizProposeNum(商业险)
     * @return 投保单号
     */
    private static String getInsPropNum(JSONObject enquiryJsonObject, String key) {
        if (enquiryJsonObject == null || enquiryJsonObject.isEmpty() || !enquiryJsonObject.containsKey("sq")) {
            return "";
        }

        JSONObject sq = enquiryJsonObject.getJSONObject("sq");
        String proposeNum = sq.getString(key);

        return (proposeNum == null || proposeNum.isEmpty() || "null".equalsIgnoreCase(proposeNum)) ? "" : proposeNum;
    }

    /**
     * 获取交强险投保单号
     */
    private static String getTrafficInsPropNum(JSONObject enquiryJsonObject) {
        return getInsPropNum(enquiryJsonObject, "efcProposeNum");
    }

    /**
     * 获取商业险投保单号
     */
    private static String getBusinessInsPropNum(JSONObject enquiryJsonObject) {
        return getInsPropNum(enquiryJsonObject, "bizProposeNum");
    }
    // End of Selection

    /**
     * 上年交强险保单号
     */
    private static void transJqpolicyno(JSONObject enquiryJsonObject, Enquiry enquiry) {
        if (enquiryJsonObject == null || enquiryJsonObject.isEmpty() || !enquiryJsonObject.containsKey("sq")) {
            return;
        }
        String sypolicyno = (String) enquiryJsonObject.getJSONObject("sq").get("jqpolicyno");
        if (StringUtil.isNoEmpty(sypolicyno)) {
            Map misc = enquiry.getMisc() != null ? enquiry.getMisc() : new HashMap<String, Object>();
            enquiry.setMisc(misc);
            misc.put("jqpolicyno", sypolicyno);
        }
    }

    /**
     * 上年商业险保单号
     */
    private static void transSypolicyno(JSONObject enquiryJsonObject, Enquiry enquiry) {
        if (enquiryJsonObject == null || enquiryJsonObject.isEmpty() || !enquiryJsonObject.containsKey("sq")) {
            return;
        }
        String sypolicyno = (String) enquiryJsonObject.getJSONObject("sq").get("sypolicyno");
        if (StringUtil.isNoEmpty(sypolicyno)) {
            Map misc = enquiry.getMisc() != null ? enquiry.getMisc() : new HashMap<String, Object>();
            enquiry.setMisc(misc);
            misc.put("sypolicyno", sypolicyno);
        }
    }

    @SuppressWarnings("unchecked")
    public static JSONObject transformCallbackTask(AutoTask autoTask) {

        JSONObject backMsg = JSONObject.parseObject(autoTask.getApplyJson());
        if (backMsg == null) {
            backMsg = new JSONObject();
        }
        try {
            if (backMsg.containsKey("trafficQuery") || (backMsg.containsKey("flag") && "pureESale".equals(backMsg.getString("flag")))) {
                callBackOthers(autoTask, backMsg);
                return backMsg;
            } else if (backMsg.containsKey("flag") && "XB".equals(backMsg.getString("flag"))) {
                callBackRenewal(autoTask, backMsg);
                return backMsg;
            }

            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
            Order order = enquiry.getOrder();

            //回写车辆信息转换
            JSONObject carInfo = (JSONObject) JSONObject.toJSON(order.getCarInfo());
            //回写受益人信息转换
            JSONArray beneficiaryPersons = (JSONArray) JSONArray.toJSON(order.getBeneficiaryPersons());
            //回写车主信息转换
            JSONObject carOwnerInfo = (JSONObject) JSONObject.toJSON(order.getCarOwnerInfo());
            //回写投保人信息转换
            JSONObject insurePerson = (JSONObject) JSONObject.toJSON(order.getInsurePerson());
            //回写被保人信息转换
            JSONArray insuredPersons = (JSONArray) JSONArray.toJSON(order.getInsuredPersons());
            //回写保险配置信息转换
            BaseSuiteInfo baseSuiteInfo = order.getSuiteInfo();
            JSONObject suiteInfo = (JSONObject) JSONObject.toJSON(baseSuiteInfo);
            JSONArray backSuite = new JSONArray();
            if (suiteInfo != null && !suiteInfo.isEmpty()) {
                if (suiteInfo.containsKey("bizSuiteInfo") && suiteInfo.getJSONObject("bizSuiteInfo") != null && suiteInfo.getJSONObject("bizSuiteInfo").getJSONObject("suites") != null) {
                    final JSONObject finalBackMsg = backMsg;
                    suiteInfo.getJSONObject("bizSuiteInfo").getJSONObject("suites").forEach((k, v) -> {
                        String companyId = autoTask.getTaskType().split("-")[1];
                        // Story#11817, 12183 【太保】处理车辆安全检测特约条款、代为驾驶服务特约条款、代为送检服务特约条款
                        if (companyId.equals("2011") &&
                            "VehicleInspection,DesignatedDriving,SendForInspection".contains(k)) {
                            processSuites(finalBackMsg, k, v, backSuite);
                            return;
                        }

                        backSuite.add(v);
                    });

                    suiteInfo.getJSONObject("bizSuiteInfo").put("suites", backSuite);
                }
            }

            if (carInfo.containsKey("firstRegDate") && Objects.nonNull(order.getCarInfo().getFirstRegDate())) {
                carInfo.put("firstRegDate", DateCalcUtil.getFormatDate(order.getCarInfo().getFirstRegDate(), "yyyy-MM-dd"));
            }
            if (carInfo.containsKey("transferDate") && Objects.nonNull(order.getCarInfo().getTransferDate())) {
                carInfo.put("transferDate", DateCalcUtil.getFormatDate(order.getCarInfo().getTransferDate(), "yyyy-MM-dd HH:mm:ss"));
            }
            backMsg.put("carInfo", carInfo);
            //回写非车险险种
            if (order.getExtSuiteInfo() != null) {
                backMsg.put("extSuiteInfo", order.getExtSuiteInfo());
            }

            //仅查询流程回写关系人信息
            if (autoTask.getTaskType().contains("query") || autoTask.getTaskType().contains("autoinsure") || autoTask.getTaskType().contains("renewalQuery")) {
                backMsg.put("beneficiaryPersons", beneficiaryPersons);
                backMsg.put("carOwnerInfo", carOwnerInfo);
                backMsg.put("applicantPersonInfo", insurePerson);
                backMsg.put("insuredPersonInfoList", insuredPersons);
            }
            backMsg.put("baseSuiteInfo", suiteInfo);

            JSONObject def = backMsg.getJSONObject("definition");
            if (Objects.isNull(def))
                def = new JSONObject();
            if (autoTask.getTempValues() != null) {
                Map<String, Object> rule = autoTask.getTempValues();
                String ruleInfo = String.valueOf(rule.getOrDefault("ruleInfo", ""));
                JSONObject temp = JSONObject.parseObject(ruleInfo);

                if (StringUtil.isNoEmpty(ruleInfo)) {
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.bizBrokerageRate))) {
                        def.put(PlatformKey.bizBrokerageRate, temp.getString(PlatformKey.bizBrokerageRate));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.efcBrokerageRate))) {
                        def.put(PlatformKey.efcBrokerageRate, temp.getString(PlatformKey.efcBrokerageRate));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.bizRiskLevel1))) {
                        def.put(PlatformKey.bizRiskLevel1, temp.getString(PlatformKey.bizRiskLevel1));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.bizRiskLevel2))) {
                        def.put(PlatformKey.bizRiskLevel2, temp.getString(PlatformKey.bizRiskLevel2));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.efcRiskLevel1))) {
                        def.put(PlatformKey.efcRiskLevel1, temp.getString(PlatformKey.efcRiskLevel1));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.efcRiskLevel2))) {
                        def.put(PlatformKey.efcRiskLevel2, temp.getString(PlatformKey.efcRiskLevel2));
                    }
                    if (StringUtil.isNoEmpty(temp.getString(PlatformKey.itemCode))) {
                        def.put(PlatformKey.itemCode, temp.getString(PlatformKey.itemCode));
                    }
                }
            }
            if (order.getPlatformInfo() != null) {
                Map<String, Object> platform = order.getPlatformInfo();
                if (platform.containsKey("definition")) {
                    def.putAll((Map) platform.get("definition"));
                }
            }
            backMsg.put("definition", def);
            JSONObject sq = backMsg.getJSONObject("sq");
            if (Objects.isNull(sq))
                sq = new JSONObject();
            if (autoTask.getTaskType().endsWith("-insure")) {
                //task-2766核保暂存任务SQ中不回写以下几项
                sq.remove(SqConversionKey.totalCharge);
                sq.remove(SqConversionKey.taxCharge);
                sq.remove(SqConversionKey.efcCharge);
                sq.remove(SqConversionKey.bizCharge);
            }
            if (Objects.nonNull(enquiry.getMisc())) {
                if (enquiry.getMisc().containsKey("rootMap") && Objects.nonNull(enquiry.getMisc().get("rootMap"))) {
                    backMsg.putAll((Map) enquiry.getMisc().remove("rootMap"));
                }
                sq.putAll(enquiry.getMisc());
            }
            if (Objects.nonNull(baseSuiteInfo)) {
                BizSuiteInfo bizSuiteInfo = baseSuiteInfo.getBizSuiteInfo();
                EfcSuiteInfo efcSuiteInfo = baseSuiteInfo.getEfcSuiteInfo();
                TaxSuiteInfo taxSuiteInfo = baseSuiteInfo.getTaxSuiteInfo();
                if (enquiry.getTotalCharge() != null && !autoTask.getTaskType().endsWith("-insure")) {
                    sq.put(SqConversionKey.totalCharge, enquiry.getTotalCharge());
                }
                if (taxSuiteInfo != null && taxSuiteInfo.getDiscountCharge() != null && !autoTask.getTaskType().endsWith("-insure")) {
                    sq.put(SqConversionKey.taxCharge, taxSuiteInfo.getDiscountCharge());
                }
                if (efcSuiteInfo != null) {
                    if (efcSuiteInfo.getDiscountCharge() != null && !autoTask.getTaskType().endsWith("-insure")) {
                        sq.put(SqConversionKey.efcCharge, efcSuiteInfo.getDiscountCharge());
                    }
                    if (efcSuiteInfo.getDiscountRate() != null && !autoTask.getTaskType().endsWith("-insure")) {
                        sq.put(SqConversionKey.trafficDiscountRate, efcSuiteInfo.getDiscountRate());
                    }
                }

                if (bizSuiteInfo != null) {
                    if (bizSuiteInfo.getDiscountCharge() != null && !autoTask.getTaskType().endsWith("-insure")) {
                        sq.put(SqConversionKey.bizCharge, bizSuiteInfo.getDiscountCharge());
                    }

                    if (bizSuiteInfo.getDiscountRate() != null && !autoTask.getTaskType().endsWith("-insure")) {
                        sq.put(SqConversionKey.bussDiscountRate, bizSuiteInfo.getDiscountRate());
                    }
                }
            }
            if (StringUtil.isNoEmpty(enquiry.getTotalTempId())) {
                sq.put(SqConversionKey.totalTempId, enquiry.getTotalTempId());
            }
            if (StringUtil.isNoEmpty(enquiry.getBizProposeNum())) {
                sq.put(SqConversionKey.bizProposeNum, enquiry.getBizProposeNum());
            }
            if (StringUtil.isNoEmpty(enquiry.getEfcProposeNum())) {
                sq.put(SqConversionKey.efcProposeNum, enquiry.getEfcProposeNum());
            }
            if (StringUtil.isNoEmpty(enquiry.getBizPolicyCode())) {
                sq.put(SqConversionKey.bizPolicyCode, enquiry.getBizPolicyCode());
            }
            if (StringUtil.isNoEmpty(enquiry.getEfcPolicyCode())) {
                sq.put(SqConversionKey.efcPolicyCode, enquiry.getEfcPolicyCode());
            }
            backMsg.put("sq", sq);
            //覆盖配置信息
            if (backMsg.containsKey("configInfo")) {
                Map<String, Object> configMap = new HashMap<>(2);
                configMap.put("configMap", autoTask.getConfigs());
                backMsg.put("configInfo", configMap);
            }
            if (Objects.nonNull(backMsg.getJSONObject("baseSuiteInfo")) && Objects.nonNull(backMsg.getJSONObject("baseSuiteInfo").getJSONObject("taxSuiteInfo")) && backMsg.getJSONObject("baseSuiteInfo").getJSONObject("taxSuiteInfo").containsKey("discountCharge")) {
                String tempCharge = backMsg.getJSONObject("baseSuiteInfo").getJSONObject("taxSuiteInfo").getString("discountCharge");
                backMsg.getJSONObject("baseSuiteInfo").getJSONObject("taxSuiteInfo").remove("discountCharge");
                backMsg.getJSONObject("baseSuiteInfo").getJSONObject("taxSuiteInfo").put("charge", tempCharge);
            }
            //电子保单回写
            if (order.getElecPolicyInfo() != null && order.getElecPolicyInfo().size() > 0) {
                backMsg.put("elecPolicyInfo", order.getElecPolicyInfo());
            }

            buildOtherInfo(def, backMsg, PlatformConstants.CODE_NAME_MAPPING);
        } catch (Exception e) {
            log.error("精灵任务：{} 回写数据转换异常：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "精灵回写数据转换异常");
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.DATA_TRANS.getCde());
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_STOP, false);
        }
        if (autoTask.getErrorInfo() != null && !autoTask.getErrorInfo().isEmpty()) {
            if (!autoTask.getErrorInfo().containsKey("stop")) {
                autoTask.getErrorInfo().put("stop", false);
            }
            backMsg.put("errorInfo", autoTask.getErrorInfo());
        }
        backMsg.put("taskStatus", autoTask.getTaskStatus());
        backMsg.put("processType", autoTask.getTempValues().get("processType"));
        backMsg.put("monitorid", autoTask.getTempValues().get("monitorid"));
        backMsg.put("taskType", autoTask.getTaskType().split("-")[2]);
        backMsg.put("businessId", autoTask.getTempValues().get("businessId"));
        backMsg.put("enquiryId", autoTask.getTempValues().get("enquiryId"));
        backMsg.put("buybusitype", "01");
        backMsg.put("insureStatus", autoTask.getTempValues().get("insureStatus"));
        backMsg.put("renewalState", autoTask.getRenewalState());
        return backMsg;
    }

    private static void buildOtherInfo(JSONObject definition, JSONObject backMsg, Map<String, String> codeCacheMap) {
        // otherInfo 回写
        JSONArray otherInfos = new JSONArray();
        if (MapUtil.isNotEmpty(codeCacheMap)) {
            definition.forEach((key, value) -> {
                if (codeCacheMap.containsKey(key)) {
                    JSONObject otherInfo = new JSONObject();
                    otherInfo.put("code", key);
                    otherInfo.put("label", codeCacheMap.get(key));
                    otherInfo.put("value", value);
                    otherInfos.add(otherInfo);
                }
            });
        }

        if (CollectionUtils.isNotEmpty(otherInfos)) {
            backMsg.put("otherInfo", otherInfos);
        }
    }

    /**
     * 从map中检查并获取对应value
     *
     * @param argMap
     * @param key
     * @return
     */
    public static String checkValue(Map argMap, String key) {
        String result = "";
        if (argMap != null && argMap.containsKey(key)) {

            if (argMap.get(key) instanceof JSONArray) {
                result = JSONArray.toJSONString(argMap.get(key));
            } else {
                result = String.valueOf(argMap.get(key));
            }

        }
        return result;
    }

    private static Object javaToJson(Object o) throws Exception {
        JSONObject json = new JSONObject();
        try {
            if (o != null) {
                json = (JSONObject) JSONObject.toJSON(o);
            }
        } catch (Exception e) {
            throw new Exception("java转换json出错：" + e.getMessage());
        }
        return json;
    }

    /**
     * 参考 SupplyParamKey
     * 投保数据补充项 放入 Enquiry 杂项 Misc 这个map中 以 supplyParam 为key 对应map结果
     *
     * @param enquiry
     * @param enquiryJsonObject
     */
    private static void initSupplyParamMap(Enquiry enquiry, JSONObject enquiryJsonObject) {
        Map<String, Object> supplyParam = new HashMap<>();
        //获取任务数据中补全信息,如果数据为空则实例空jsonArray 防止nullpoint;
        JSONArray supplyParamArry = enquiryJsonObject.getJSONArray(SupplyParamKey.supplyParam);

        if (CollUtil.isNotEmpty(supplyParamArry)) {
            for (int i = 0; i < supplyParamArry.size(); i++) {
                JSONObject temp = supplyParamArry.getJSONObject(i);
                if (!StringUtil.checkjsonValueEmpty(temp, "itemcode")) {
                    supplyParam.put(temp.getString("itemcode"), temp.get("itemvalue"));
                }
            }
            enquiry.getMisc().put(SupplyParamKey.supplyParam, supplyParam);
        }
    }

    /**
     * 参考 SupplyParamKey
     * 影像资料地址 这个map中 以 supplyParam 为key 对应map结果
     *
     * @param enquiry
     * @param enquiryJsonObject
     */
    private static void initImgAddress(Enquiry enquiry, JSONObject enquiryJsonObject) {
        JSONObject imgAddressJson = enquiryJsonObject.getJSONObject("imgAddress");

        if (CollUtil.isNotEmpty(imgAddressJson)) {
            enquiry.getOrder().setImgAddress((Map) imgAddressJson);
        }
    }

    /**
     * 2017年4月20日 16:48:05
     * 补充数据方法 1.0 修改 投保人 被保人 车主 电话号码 邮箱 身份证地址
     * 地址逻辑
     * 1.如果传过来有被保人和投保人的地址，则精灵对应取值，无需覆盖。
     * 2.如果只传了其中一个，则精灵将该地址赋值到其他地址上。
     * 3.目前规则只会配被保人地址，已经可以满足业务需求
     * <p>
     * 邮箱：
     * 1.如果传过来有被保人和投保人的邮箱，则精灵对应取值，无需覆盖。
     * 2.如果只传了其中一个，则精灵将该邮箱赋值到其他邮箱上。
     * 3.目前规则只会配投保人邮箱，已经可以满足业务需求
     */
    private static void supplyParamConversion(Enquiry enquiry, Map<String, String> supplyParamMap) {
        String ownerEmail = "", ownerAddress = "", insuredEmail = "", insuredAddress = "", applicantEmail = "", applicantAddress = "";
        //被投保人
        InsurePerson insurePerson = enquiry.getOrder().getInsuredPersons().get(0);
        //投保人
        InsurePerson applicant = enquiry.getOrder().getInsurePerson();
        //车主
        CarOwnerInfo carOwnerInfo = enquiry.getOrder().getCarOwnerInfo();

        if (supplyParamMap != null && !supplyParamMap.isEmpty()) {
            if (supplyParamMap.containsKey(SupplyParamKey.applicantAddress) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.applicantAddress))) {
                //投保人身份证地址
                applicantAddress = supplyParamMap.get(SupplyParamKey.applicantAddress);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.insuredAddress) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.insuredAddress))) {
                //被保人身份证地址
                insuredAddress = supplyParamMap.get(SupplyParamKey.insuredAddress);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.ownerAddress) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.ownerAddress))) {
                //车主身份证地址
                ownerAddress = supplyParamMap.get(SupplyParamKey.ownerAddress);

            }

            if (supplyParamMap.containsKey(SupplyParamKey.applicantEmail) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.applicantEmail))) {
                //投保人邮箱
                applicantEmail = supplyParamMap.get(SupplyParamKey.applicantEmail);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.insuredEmail) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.insuredEmail))) {
                //被投保人邮箱
                insuredEmail = supplyParamMap.get(SupplyParamKey.insuredEmail);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.ownerEmail) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.ownerEmail))) {
                //车主邮箱
                ownerEmail = supplyParamMap.get(SupplyParamKey.ownerEmail);

            }

            String tempAddress = "";

            //地址赋值处理 被保人地址不为空 投保人为空 被保人地址覆盖所有关系人地址信息
            if (StringUtil.isNoEmpty(insuredAddress) && StringUtil.isEmpty(applicantAddress) && StringUtil.isEmpty(ownerAddress)) {
                tempAddress = insuredAddress;
                log.info("获取到‘被保人’身份地址信息，以‘被保人’地址为准全部关系人覆盖");
            }
            //地址赋值处理 投保人地址不为空 被保人为空 投保人地址覆盖所有关系人地址信息
            if (StringUtil.isNoEmpty(applicantAddress) && StringUtil.isEmpty(insuredAddress) && StringUtil.isEmpty(ownerAddress)) {
                tempAddress = applicantAddress;
                log.info("获取到‘投保人’身份地址信息，以‘投保人’地址为准全部关系人覆盖");
            }
            if (StringUtil.isNoEmpty(tempAddress)) {
                //投保人
                applicant.setAddress(tempAddress);
                //被保人
                insurePerson.setAddress(tempAddress);
                //车主
                carOwnerInfo.setAddress(tempAddress);
            }
            if (StringUtil.isNoEmpty(insuredAddress)) {
                //被保人
                insurePerson.setAddress(insuredAddress);
            }
            if (StringUtil.isNoEmpty(applicantAddress)) {
                //投保人
                applicant.setAddress(applicantAddress);
            }
            if (StringUtil.isNoEmpty(ownerAddress)) {
                //车主
                carOwnerInfo.setAddress(ownerAddress);
            }

            String tempEmail = "";
            //地址赋值处理 投保人邮箱不为空 被保人为空 投保人邮箱覆盖所有关系人邮箱信息
            if (StringUtil.isNoEmpty(applicantEmail) && StringUtil.isEmpty(insuredEmail) && StringUtil.isEmpty(ownerEmail)) {
                tempEmail = applicantEmail;
            }
            //地址赋值处理 被保人邮箱不为空 投保人为空 被保人邮箱覆盖所有关系人邮箱信息
            if (StringUtil.isNoEmpty(insuredEmail) && StringUtil.isEmpty(applicantEmail) && StringUtil.isEmpty(ownerEmail)) {
                tempEmail = insuredEmail;
            }
            if (StringUtil.isNoEmpty(tempEmail)) {
                //投保人
                applicant.setEmail(tempEmail);
                //被保人
                insurePerson.setEmail(tempEmail);
                //车主
                carOwnerInfo.setEmail(tempEmail);
            }
            if (StringUtil.isNoEmpty(insuredEmail)) {
                //被保人
                insurePerson.setEmail(insuredEmail);
            }
            if (StringUtil.isNoEmpty(applicantEmail)) {
                //投保人
                applicant.setEmail(applicantEmail);
            }
            if (StringUtil.isNoEmpty(ownerEmail)) {
                //车主
                carOwnerInfo.setEmail(ownerEmail);
            }

            String tempMobile = "";
            //投保人补充数据
            if (supplyParamMap.containsKey(SupplyParamKey.applicantMobile) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.applicantMobile))) {
                tempMobile = supplyParamMap.get(SupplyParamKey.applicantMobile);
                //投保人手机号码
                applicant.setMobile(tempMobile);
                applicant.setPhone(tempMobile);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.insuredMobile) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.insuredMobile))) {
                tempMobile = supplyParamMap.get(SupplyParamKey.insuredMobile);
                //被投保人手机号码
                insurePerson.setMobile(tempMobile);
                insurePerson.setPhone(tempMobile);
            }

            if (supplyParamMap.containsKey(SupplyParamKey.ownerMobile) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.ownerMobile))) {
                tempMobile = supplyParamMap.get(SupplyParamKey.ownerMobile);
                //车主手机号码
                carOwnerInfo.setPhone(tempMobile);
                carOwnerInfo.setMobile(tempMobile);
            }

            //权益索赔人补充数据
            BeneficiaryPerson beneficiaryPerson = enquiry.getOrder().getBeneficiaryPersons().get(0);

            if (supplyParamMap.containsKey(SupplyParamKey.claimantName) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.claimantName))) {
                beneficiaryPerson.setName(supplyParamMap.get(SupplyParamKey.claimantName));
            }
            //权益索赔人证件类型
            if (supplyParamMap.containsKey(SupplyParamKey.claimantDocumentType) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.claimantDocumentType))) {
                beneficiaryPerson.setIdCardType(Integer.parseInt(supplyParamMap.get(SupplyParamKey.claimantDocumentType)));
            }
            //权益索赔人证件号码
            if (supplyParamMap.containsKey(SupplyParamKey.claimantDocumentNumber) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.claimantDocumentNumber))) {
                beneficiaryPerson.setIdCard(supplyParamMap.get(SupplyParamKey.claimantDocumentNumber));
            }
            //权益索赔人手机号码
            if (supplyParamMap.containsKey(SupplyParamKey.claimantMobile) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.claimantMobile))) {
                beneficiaryPerson.setPhone(supplyParamMap.get(SupplyParamKey.claimantMobile));
                beneficiaryPerson.setMobile(supplyParamMap.get(SupplyParamKey.claimantMobile));
            }
            //权益索赔人邮箱
            if (supplyParamMap.containsKey(SupplyParamKey.claimantEmail) && StringUtil.isNoEmpty(supplyParamMap.get(SupplyParamKey.claimantEmail))) {
                beneficiaryPerson.setEmail(supplyParamMap.get(SupplyParamKey.claimantEmail));
            }

            List<BeneficiaryPerson> beneficiaryPersons = new ArrayList<>();
            beneficiaryPersons.add(beneficiaryPerson);
            enquiry.getOrder().setBeneficiaryPersons(beneficiaryPersons);
        } else {
            log.info("未获取到补充信息");
        }
    }

    /**
     * 拼装交管信息回写
     *
     * @param autoTask
     * @param backMsg
     * @return
     */
    private static JSONObject callBackOthers(AutoTask autoTask, JSONObject backMsg) {
        try {
            JSONObject jsonObject = new JSONObject();
            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();

            if (enquiry.getOrder() != null && enquiry.getOrder().getCarInfo() != null) {
                CarInfo carInfo = enquiry.getOrder().getCarInfo();
                if (StringUtil.isNoEmpty(carInfo.getPlateNum())) {
                    jsonObject.put("licenseNo", carInfo.getPlateNum());
                }
                if (StringUtil.isNoEmpty(carInfo.getVin())) {
                    jsonObject.put("vinNo", carInfo.getVin());
                }
                if (StringUtil.isNoEmpty(carInfo.getEngineNum())) {
                    jsonObject.put("engineNo", carInfo.getEngineNum());
                }
                if (carInfo.getFirstRegDate() != null) {
                    jsonObject.put("enrollDate", DateCalcUtil.getFormatDate(carInfo.getFirstRegDate(), "yyyyMMdd"));
                }
                if (StringUtil.isNoEmpty(carInfo.getCarBrandName())) {
                    jsonObject.put("carBrandName", carInfo.getCarBrandName());
                }
                if (StringUtil.isNoEmpty(carInfo.getInsuranceCode())) {
                    jsonObject.put("modelCode", carInfo.getInsuranceCode());
                }
                if (carInfo.getFullLoad() != null) {
                    jsonObject.put("fullLoad", carInfo.getFullLoad());
                }
                if (carInfo.getDisplacement() != null) {
                    jsonObject.put("displacement", carInfo.getDisplacement());
                }
                if (carInfo.getSeatCnt() != null) {
                    jsonObject.put("seatCnt", carInfo.getSeatCnt());
                }
                if (carInfo.getModelLoad() != null) {
                    jsonObject.put("modelLoad", carInfo.getModelLoad());
                }
                if (carInfo.getTransferDate() != null) {
                    jsonObject.put("transferDate", DateCalcUtil.getFormatDate(carInfo.getTransferDate(), "yyyy-MM-dd 00:00:00"));
                }
                if (carInfo.getJgVehicleType() != null) {
                    jsonObject.put("jgVehicleType", carInfo.getJgVehicleType());
                }
                Map<String, Object> tempvalues = autoTask.getTempValues();
                if (tempvalues != null && tempvalues.containsKey("pureESale") && StringUtil.isNoEmpty(tempvalues.get("pureESale").toString())) {
                    jsonObject.put("pureESale", tempvalues.get("pureESale").toString());
                }
            }
            if (enquiry.getOrder() != null && enquiry.getOrder().getCarOwnerInfo() != null) {
                if (!Strings.isNullOrEmpty(enquiry.getOrder().getCarOwnerInfo().getName())) {
                    jsonObject.put("carOwnerName", enquiry.getOrder().getCarOwnerInfo().getName());
                }
            }
            backMsg.put("trafficInfo", jsonObject);

        } catch (Exception e) {
            log.error("精灵任务：{} 回写数据转换异常：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "转换交管信息回写出错");
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.TRAFFICQUERY_ERROR.getCde());
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_STOP, false);
        }

        if (autoTask.getErrorInfo() != null && !autoTask.getErrorInfo().isEmpty()) {
            if (!autoTask.getErrorInfo().containsKey("stop")) {
                autoTask.getErrorInfo().put("stop", false);
            }
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.TRAFFICQUERY_ERROR.getCde());
            backMsg.put("errorInfo", autoTask.getErrorInfo());//stop丢了
        } else {
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "交管信息回写成功");
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, "18");//交管查询成功固定传18
            autoTask.setResultStr("交管信息回写成功");
            backMsg.put("errorInfo", autoTask.getErrorInfo());
        }

        return backMsg;
    }

    /**
     * car.specific.license 车辆信息-车牌
     * lastPoliceyNum 上年商业险投保单号
     */
    //续保查询信息补充
    public static Enquiry renewalInfo(String taskEntityStr) {
        if (log.isDebugEnabled()) {
            log.debug("申请到的任务数据{}", taskEntityStr);
        }
        JSONObject enquiryJsonObject = JSONObject.parseObject(taskEntityStr);
        Enquiry enquiry = new Enquiry();
        String license = enquiryJsonObject.getJSONObject("inParas").getString("car.specific.license");
        String policeyNum = enquiryJsonObject.getJSONObject("inParas").getString("application.commercial.lastPoliceyNum");
        Map<String, String> renewalMap = new HashMap<String, String>();

        if (license != null && !"".equals(license)) {
            renewalMap.put("carLicense", license);
        }
        if (policeyNum != null && !"".equals(policeyNum)) {
            renewalMap.put("lastPoliceyNum", policeyNum);
        }

        if (enquiry.getMisc() == null) {
            enquiry.setMisc(new HashMap());
        }
        if (enquiry.getOrder() == null) {
            enquiry.setOrder(new Order());
        }
        //投保人
        if (enquiry.getOrder().getInsurePerson() == null) {
            enquiry.getOrder().setInsurePerson(new InsurePerson());
        }
        //被保人
        if (enquiry.getOrder().getInsuredPersons() == null) {
            List<InsurePerson> insuredPersons = new ArrayList<InsurePerson>();
            insuredPersons.add(new InsurePerson());
            enquiry.getOrder().setInsuredPersons(insuredPersons);
        }
        //车主
        if (enquiry.getOrder().getCarOwnerInfo() == null) {
            enquiry.getOrder().setCarOwnerInfo(new CarOwnerInfo());
        }
        //权益人
        if (enquiry.getOrder().getBeneficiaryPersons() == null) {
            List<BeneficiaryPerson> insuredPersons = new ArrayList<BeneficiaryPerson>();
            insuredPersons.add(new BeneficiaryPerson());
            enquiry.getOrder().setBeneficiaryPersons(insuredPersons);
        }
        //车信息
        if (enquiry.getOrder().getCarInfo() == null) {
            enquiry.getOrder().setCarInfo(new CarInfo());
        }
        //险种主对象
        if (enquiry.getOrder().getSuiteInfo() == null) {
            enquiry.getOrder().setSuiteInfo(new BaseSuiteInfo());
            enquiry.getOrder().getSuiteInfo().setBizSuiteInfo(new BizSuiteInfo());
            enquiry.getOrder().getSuiteInfo().setEfcSuiteInfo(new EfcSuiteInfo());
            enquiry.getOrder().getSuiteInfo().setTaxSuiteInfo(new TaxSuiteInfo());
        }

        enquiry.getMisc().putAll(renewalMap);
        return enquiry;
    }

    /**
     * 拼装续保查询回写
     *
     * @param autoTask
     * @param backMsg
     * @return
     */
    private static JSONObject callBackRenewal(AutoTask autoTask, JSONObject backMsg) {
        try {
            JSONObject jsonObject = new JSONObject();
            Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();

            //车辆信息
            if (enquiry.getOrder().getCarInfo() != null) {
                CarInfo carInfo = enquiry.getOrder().getCarInfo();
                if (carInfo.getCarUserType() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_USERTYPE, carInfo.getCarUserType().toString());
                }
                if (carInfo.getUseProps() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_USEPROPS, carInfo.getUseProps().toString());
                }
                if (carInfo.getPlateNum() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_LICENSE, carInfo.getPlateNum());
                }
                if (carInfo.getSeatCnt() != null) {
                    jsonObject.put(TaskEntryKey.CAR_MODEL_SEATS, carInfo.getSeatCnt().toString());
                }
                if (carInfo.getModelLoad() != null) {
                    jsonObject.put(TaskEntryKey.CAR_MODEL_MODELLOAD, carInfo.getModelLoad().toString());
                }
                if (carInfo.getFullLoad() != null) {
                    jsonObject.put(TaskEntryKey.CAR_MODEL_FULLLOAD, carInfo.getFullLoad().toString());
                }
                if (carInfo.getDisplacement() != null) {
                    jsonObject.put(TaskEntryKey.CAR_MODEL_DISPLACEMENT, carInfo.getDisplacement().toString());
                }
                if (carInfo.getPrice() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_PRICE, carInfo.getPrice().toString());
                }
                if (carInfo.getEngineNum() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_ENGINENUM, carInfo.getEngineNum());
                }
                if (carInfo.getVin() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_VIN, carInfo.getVin());
                }
                if (carInfo.getFirstRegDate() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_REGDATE, DateCalcUtil.getFormatDate(carInfo.getFirstRegDate(), "yyyy-MM-dd"));
                }
                if (carInfo.getCarModelName() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_MODEL, carInfo.getCarModelName());
                }
                if (carInfo.getTransferDate() != null) {
                    jsonObject.put(TaskEntryKey.CAR_SPECIFIC_TRANSFER_DATE, DateCalcUtil.getFormatDate(carInfo.getTransferDate(), "yyyy-MM-dd 00:00:00"));
                }

            }

            //车主
            if (enquiry.getOrder().getCarOwnerInfo() != null) {
                CarOwnerInfo carInfo = enquiry.getOrder().getCarOwnerInfo();
                if (StringUtil.isNoEmpty(carInfo.getName())) {
                    jsonObject.put(TaskEntryKey.CAR_OWNER_NAME, carInfo.getName());
                    jsonObject.put(TaskEntryKey.CAR_OWNER_CERTIFICATE, carInfo.getIdCardType().toString());
                    jsonObject.put(TaskEntryKey.CAR_OWNER_IDNUMBER, carInfo.getIdCard());
                }
            }

            //投保人
            if (enquiry.getOrder().getInsurePerson() != null) {
                InsurePerson carInfo = enquiry.getOrder().getInsurePerson();
                if (StringUtil.isNoEmpty(carInfo.getName())) {
                    jsonObject.put(TaskEntryKey.PROPOSER_NAME, carInfo.getName());
                    jsonObject.put(TaskEntryKey.PROPOSER_CERTIFICATE, carInfo.getIdCardType().toString());
                    jsonObject.put(TaskEntryKey.PROPOSER_IDNUMBER, carInfo.getIdCard());
                }
            }

            //被保人
            if (enquiry.getOrder().getInsuredPersons() != null) {
                List<InsurePerson> carInfo = enquiry.getOrder().getInsuredPersons();
                if (StringUtil.isNoEmpty(carInfo.get(0).getName())) {
                    JSONArray array = new JSONArray();
                    JSONObject persons = new JSONObject();
                    persons.put(TaskEntryKey.INSURED_NAME, carInfo.get(0).getName());
                    persons.put(TaskEntryKey.INSURED_CERTIFICATE, carInfo.get(0).getIdCardType().toString());
                    persons.put(TaskEntryKey.INSURED_IDNUMBER, carInfo.get(0).getIdCard());
                    array.add(0, persons);
                    jsonObject.put(TaskEntryKey.INSURED, array);
                }
            }

            //商业险
            if (enquiry.getOrder().getSuiteInfo().getBizSuiteInfo() != null) {
                if (enquiry.getOrder().getSuiteInfo().getBizSuiteInfo().getSuites() != null) {
                    List<SuiteDef> suiteDefs = com.google.common.collect.Lists.<SuiteDef>newArrayList(enquiry.getOrder().getSuiteInfo().getBizSuiteInfo().getSuites().values());
                    if (suiteDefs.size() > 0) {
                        for (SuiteDef suiteDef : suiteDefs) {
                            String code = suiteDef.getCode();
                            if (StringUtil.isNoEmpty(code)) {
                                String amount = suiteDef.getAmount().toString();
                                jsonObject.put("insureItem." + RiskItem.getItemsCode().get(code) + ".code", code);
                                jsonObject.put("insureItem." + RiskItem.getItemsCode().get(code) + ".name", RiskItem.getItemsName().get(code));
                                if (code.contains("Ncf")) {
                                    jsonObject.put("insureItem." + RiskItem.getItemsCode().get(code) + ".current", "1");
                                } else {
                                    if ("GlassIns".equals(code)) {
                                        if ("0".equals(amount)) {
                                            jsonObject.put("insureItem." + code + ".current", "1");
                                        } else if ("1".equals(amount)) {
                                            jsonObject.put("insureItem." + code + ".current", "2");
                                        }
                                    } else if ("MirrorLightIns".equals(code)) {
                                        if ("0".equals(amount)) {
                                            jsonObject.put("insureItem." + code + ".current", "2");
                                        } else if ("1".equals(amount)) {
                                            jsonObject.put("insureItem." + code + ".current", "3");
                                        }
                                    } else if ("VehicleDemageMissedThirdPartyCla".equals(code)) {
                                        jsonObject.put("insureItem." + RiskItem.getItemsCode().get(code) + ".current", "1");
                                    } else {
                                        jsonObject.put("insureItem." + RiskItem.getItemsCode().get(code) + ".current", amount);
                                    }
                                }
                            }
                        }
                        String stat = enquiry.getOrder().getSuiteInfo().getBizSuiteInfo().getStart();
                        String end = enquiry.getOrder().getSuiteInfo().getBizSuiteInfo().getEnd();
                        jsonObject.put(TaskEntryKey.APPLICATION_COMMERCIAL_EFFECTIVEDATE, stat);
                        jsonObject.put(TaskEntryKey.APPLICATION_COMMERCIAL_EXPIRYDATE, end);

                        if (enquiry.getBizPolicyCode() != null) {
                            jsonObject.put(TaskEntryKey.APPLICATION_COMMERCIAL_LASTPOLICEYNUM, enquiry.getBizPolicyCode());
                        }
                    }
                }

            }

            //交强险
            if (enquiry.getOrder().getSuiteInfo().getEfcSuiteInfo() != null) {
                EfcSuiteInfo carInfo = enquiry.getOrder().getSuiteInfo().getEfcSuiteInfo();
                if (carInfo.getDiscountCharge() != null) {
                    jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLECOMPULSORYINS_NAME, "机动车交通事故责任强制险");
                    jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLECOMPULSORYINS_CODE, "VehicleCompulsoryIns");
                    jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLECOMPULSORYINS_CURRENT, "1");
                    String stat = enquiry.getOrder().getSuiteInfo().getEfcSuiteInfo().getStart();
                    String end = enquiry.getOrder().getSuiteInfo().getEfcSuiteInfo().getEnd();
                    jsonObject.put(TaskEntryKey.APPLICATION_COMPULSORY_EFFECTIVEDATE, stat);
                    jsonObject.put(TaskEntryKey.APPLICATION_COMPULSORY_EXPIRYDATE, end);
                    jsonObject.put(TaskEntryKey.COMPULSORY_LASTPOLICEYNUM, enquiry.getEfcPolicyCode());
                    //车船税
                    if (enquiry.getOrder().getSuiteInfo().getTaxSuiteInfo() != null) {
                        jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLETAX_NAME, "车船税");
                        jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLETAX_CODE, "VehicleTax");
                        jsonObject.put(TaskEntryKey.INSUREITEM_VEHICLETAX_CURRENT, "1");
                    }
                }
            }
            if (enquiry.getMisc() != null) {
                if (enquiry.getMisc().containsKey("errorType")) {
                    jsonObject.put("errorType", enquiry.getMisc().get("errorType"));
                }
                if (enquiry.getMisc().containsKey("errorMessage")) {
                    jsonObject.put("errorMessage", enquiry.getMisc().get("errorMessage"));
                }
            }
            if (!jsonObject.containsKey("errorType")) {
                jsonObject.put("errorType", "其他");
            }
            if (!jsonObject.containsKey("errorMessage")) {
                jsonObject.put("errorMessage", autoTask.getResultStr());
            }
            if ("{}".equals(jsonObject.toString())) {
                jsonObject.put("tips", ExceptionCde.RENEWAL_ERROR.getCde());
            }
            backMsg.put("item", jsonObject);

        } catch (Exception e) {
            log.error("精灵任务：{} 回写数据转换异常：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "转换续保查询信息回写出错");
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.RENEWAL_ERROR.getCde());
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_STOP, false);

            final HashMap<String, Object> xbMsg = new HashMap<>(2);
            xbMsg.put("tips", ExceptionCde.RENEWAL_ERROR.getCde());
            xbMsg.put("errorType", "保网系统异常");
            xbMsg.put("errorMessage", "转换续保查询信息回写出错");
            backMsg.put("item", xbMsg);
        }

        if (autoTask.getErrorInfo() != null && !autoTask.getErrorInfo().isEmpty()) {
            if (!autoTask.getErrorInfo().containsKey("stop")) {
                autoTask.getErrorInfo().put("stop", false);
            }
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "转换续保查询信息回写出错");
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.RENEWAL_ERROR.getCde());
        } else {
            autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "续保查询回写成功");
            autoTask.setResultStr("续保查询回写成功");
        }

        return backMsg;
    }

    public static boolean useVpn(AutoTask autoTask) {
        if (autoTask != null) {
            try {
                Map<String, Object> configs = autoTask.getConfigs();
                if (configs != null) {
                    if (configs.containsKey("useVpn")) {
                        return Boolean.parseBoolean(configs.get("useVpn").toString());
                    } else if (configs.containsKey(HttpSender.proxyHost)
                        && configs.containsKey(HttpSender.proxyPort)) {
                        return true;
                    }
                }
            } catch (Exception ex) {
                return false;
            }
        }
        return false;
    }

    public static CloseableHttpClient createHttpClientWithProxy(AutoTask task, boolean useNewSSL) throws Exception {
        CloseableHttpClient closeableHttpClient = null;
        Map configMap = task.getConfigs();
        if (configMap.containsKey(HttpSender.proxyHost)
            && configMap.containsKey(HttpSender.proxyPort)
            && configMap.containsKey(HttpSender.proxyName)
            && configMap.containsKey(HttpSender.proxyPass)) {
            closeableHttpClient = buildHttpClient(true, configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()), configMap.get(HttpSender.proxyName).toString(), configMap.get(HttpSender.proxyPass).toString(), useNewSSL ? HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}) : null);
        } else {
            //刷新失败也要关闭旧链接再次登陆。免得保险公司策略认为多次同链接登陆。没有注销，有异常行为
            boolean usePool = Boolean.parseBoolean(configMap.getOrDefault(KeepSessionConfig.USE_POOL_CONNECT, "true").toString());
            if (!useNewSSL)
                closeableHttpClient = buildHttpClient(usePool);
            else
                closeableHttpClient = buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
        }
        return closeableHttpClient;
    }

    private static void processSuites(JSONObject backMsg, String suiteCode, Object originSuite, JSONArray backSuite) {
        JSONObject baseSuiteInfo = backMsg.getJSONObject("baseSuiteInfo");
        JSONObject bizSuiteInfo = baseSuiteInfo.getJSONObject("bizSuiteInfo");
        JSONArray suites = bizSuiteInfo.getJSONArray("suites");
        suites.stream()
            .filter(suite ->
                suiteCode.equals(((JSONObject) suite).getString("code"))
            )
            .findFirst()
            .ifPresent(dest -> {
                JSONObject origin = (JSONObject) originSuite;
                if ("VehicleInspection".equals(suiteCode)) { // 车辆安全检测特约条款
                    // vehicleInspectionLevel: 车辆安全检测特约条款等级
                    String code = ((JSONObject) dest).getString("vehicleInspectionLevel");
                    if (StringUtils.isNotEmpty(code)) {
                        origin.put("vehicleInspectionLevel", code);
                    }
                }

                if ("DesignatedDriving".equals(suiteCode)) { // 代为驾驶服务特约条款
                    // designatedDrivingMile: 代为驾驶服务特约条款公里数
                    String code = ((JSONObject) dest).getString("designatedDrivingMile");
                    if (StringUtils.isNotEmpty(code)) {
                        origin.put("designatedDrivingMile", code);
                    }
                }

                if ("SendForInspection".equals(suiteCode)) { // 代为送检服务特约条款
                    // sendForInspectionLevel - 代为送检等级
                    String code = ((JSONObject) dest).getString("sendForInspectionLevel");
                    if (StringUtils.isNotEmpty(code)) {
                        origin.put("sendForInspectionLevel", code);
                    }
                }

                backSuite.add(origin);
            });
    }


    /**
     * 请求报文转Java对象
     *
     * @param jsonObject 请求报文
     * @param key        键
     * @param clazz      Class 类型
     * @param consumer   set方法
     */
    private static <T> void jsonToJavaObject(JSONObject jsonObject, String key, Class<T> clazz, Consumer<T> consumer) {
        JSONObject json = jsonObject.getJSONObject(key);
        consumer.accept(JSONObject.toJavaObject(json, clazz));
    }

    private static <T> void jsonArrayToJavaObjectList(JSONObject jsonObject, String key, Class<T> clazz, Consumer<List<T>> consumer) {
        JSONArray jsonArray = jsonObject.getJSONArray(key);
        if (CollUtil.isNotEmpty(jsonArray)) {
            List<T> result = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                result.add(JSONObject.toJavaObject(jsonArray.getJSONObject(i), clazz));
            }
            consumer.accept(result);
        }
    }

    /**
     * 设置值
     * 排除null值
     *
     * @param jsonObject 请求报文
     * @param key        键
     * @param consumer   set方法
     */
    private static void setValue(JSONObject jsonObject, String key, Consumer<String> consumer) {
        if (jsonObject.containsKey(key)) {
            String value = jsonObject.getString(key);
            if (StrUtil.isNotBlank(value) && !"null".equalsIgnoreCase(value)) {
                consumer.accept(value);
            }
        }
    }

}
