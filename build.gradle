plugins {
    id 'java'
    id 'org.springframework.boot' version "$spring_boot_version"
    id "com.vanniktech.dependency.graph.generator" version "$dependency_graph_version"
}

subprojects {
    group 'com.cheche365.bc'
    version = '1.1.9-SNAPSHOT'

    apply plugin: 'java-library'
    apply plugin: 'groovy'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'pmd'

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
    }

    [compileJava, compileTestJava, compileGroovy, compileTestGroovy, javadoc]*.options*.encoding = 'UTF-8'

    tasks.withType(GroovyCompile).configureEach {
        options.compilerArgs.add("-parameters")
    }

    repositories {

        maven { url "https://maven.aliyun.com/nexus/content/groups/public/" }
        maven {
            url "http://192.168.1.251:8081/nexus/content/repositories/releases/"
            allowInsecureProtocol true
        }
        maven {
            url "http://192.168.1.251:8081/nexus/content/repositories/snapshots/"
            allowInsecureProtocol true
        }
        // bytedance-hbase repository
        maven {
            url "https://artifacts-cn-beijing.volces.com/repository/hbase/"
            name "bytedance-hbase"
        }
        // 使用本地maven库
        //mavenLocal()
        // 使用中央mav库
        mavenCentral()

    }

    configurations {
        all*.exclude group: 'org.springframework.boot', module: 'spring-boot-starter-tomcat'
        all*.exclude group: 'javax.servlet', module: 'servlet-api'

        configureEach {
            resolutionStrategy.cacheDynamicVersionsFor 10, 'MINUTES'
        }
    }

    dependencies {

        implementation "org.codehaus.groovy:groovy-all:$groovy_version"
        // 使用spock时不使用内部groovy
        testImplementation("org.spockframework:spock-core:$spock_version") {
            exclude group: 'org.codehaus.groovy', module: 'groovy-all'
        }
        testImplementation("org.spockframework:spock-spring:$spock_version") {
            exclude group: 'org.codehaus.groovy', module: 'groovy-all'
        }

        // Spring Framework
        implementation "org.springframework.boot:spring-boot"
        testImplementation "org.springframework.boot:spring-boot-starter-test"

        annotationProcessor "org.projectlombok:lombok:$lombok_version"
        compileOnly "org.projectlombok:lombok:$lombok_version"

        api "org.apache.commons:commons-lang3:$apache_common_version",
                "com.google.guava:guava:$guava_version"

        implementation("com.cheche365.bi:log-spring-boot-starter:$bi_starter_version") {
            exclude(group: 'org.springframework.boot', module: 'spring-boot-starter-test')
        }
        implementation("com.cheche365.redisson:redisson:$cheche_redisson_version") {
            exclude(group: 'org.springframework.boot', module: 'spring-boot-starter-aop')
        }
    }

    pmd {
        consoleOutput = true
        ignoreFailures = true
        ruleSets = ["category/java/errorprone.xml", "category/java/bestpractices.xml"]
    }

    bootRun {
        jvmArgs = [
                "--add-opens=java.base/java.lang=ALL-UNNAMED",
                "--add-opens=java.base/java.math=ALL-UNNAMED",
                "--add-opens=java.base/java.util=ALL-UNNAMED",
                "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
                "--add-opens=java.base/java.net=ALL-UNNAMED",
                "--add-opens=java.base/java.text=ALL-UNNAMED",
                "--add-opens=java.base/java.nio=ALL-UNNAMED"
        ]
    }

    tasks.register("bootRunDev") {
        group = "application"
        description = "Runs the Spring Boot application with the dev profile"
        doFirst {
            tasks.bootRun.configure {
                systemProperty("spring.profiles.active", "dev")
            }
        }
        finalizedBy("bootRun")
    }

    tasks.named('test', Test) {
        useJUnitPlatform()
    }

    bootJar {
        duplicatesStrategy = DuplicatesStrategy.EXCLUDE
    }
}
