package com.cheche365.bc.handler.edi;

import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.enums.InsCompanyEnum;
import com.cheche365.bc.enums.ProcessTypeEnum;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.utils.TaskUtil;
import com.cheche365.bc.utils.encrypt.AesDesEncryption;
import com.cheche365.bc.utils.encrypt.EncryptEnum;
import com.cheche365.bc.utils.encrypt.Hex;
import com.cheche365.bc.utils.encrypt.MD5;
import com.cheche365.bc.utils.sender.HttpSender;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.stereotype.Component;

import java.util.Base64;

import static com.cheche365.bc.message.TaskType.CLUE;
import static com.cheche365.bc.utils.sender.HttpSender.JSON_HEADERS;
import static com.cheche365.bc.utils.sender.HttpSender.buildHttpClient;

@Component
public class GuoShouCaiHandler implements BaseHandler {

    @Override
    public String getMethodType() {
        return ProcessTypeEnum.EDI.getKey() + InsCompanyEnum.GPIC.getCode();
    }

    @Override
    public String execute(AutoTask autoTask, String requestBody, String charSet, String url, CloseableHttpClient closeableHttpClient) throws Exception {
        if (autoTask.getTaskTypeName().equals(CLUE.code)) {
            String base64Result = Base64.getEncoder().encodeToString(requestBody.getBytes());
            String key = (String) autoTask.getConfigs().get("aesKey");
            String md5Key = (String) autoTask.getConfigs().get("md5Key");

            AesDesEncryption build = AesDesEncryption.builder()
                .cipherAlgorithm(EncryptEnum.AlgorithmEnum.AES_ECB_PKCS5Padding)
                .key(key)
                .keyFormat(EncryptEnum.KeyFormatEnum.UTF_8)
                .resultType(EncryptEnum.ResultTypeEnum.Hex).build();

            String encryptResult = build.encrypt(base64Result);
            String sign = MD5.toHex(base64Result + md5Key);

            JSONObject requestObject = new JSONObject();
            requestObject.put("data", encryptResult);
            requestObject.put("sign", sign);

            String response = HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestObject.toString(), autoTask.getParams(), JSON_HEADERS, charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
            String data = JSONObject.parseObject(response).getString("data");
            return new String(Base64.getDecoder().decode(build.decrypt(Hex.decode(data))));
        } else {
            closeableHttpClient = buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
            return HttpSender.doPostWithRetry(5, closeableHttpClient, true, url, requestBody, autoTask.getParams(), autoTask.getReqHeaders(), charSet, TaskUtil.buildReqConfig(autoTask), autoTask.keepSessionKey());
        }
    }
}
