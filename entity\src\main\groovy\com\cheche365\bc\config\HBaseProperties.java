package com.cheche365.bc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "hbase.conf")
public class HBaseProperties {

    private String zkQuorum;

    private String zkZoneParent;

    private String username;

    private String password;

    public String getZkQuorum() {
        return zkQuorum;
    }

    public void setZkQuorum(String zkQuorum) {
        this.zkQuorum = zkQuorum;
    }

    public String getZkZoneParent() {
        return zkZoneParent;
    }

    public void setZkZoneParent(String zkZoneParent) {
        this.zkZoneParent = zkZoneParent;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
