package com.cheche365.bc.config;


import com.google.common.base.Strings;
import com.volcengine.hbase.security.AuthProviderSelector;
import com.volcengine.hbase.security.plain.internals.PlainSaslClientAuthenticationProvider;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.HConstants;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.security.provider.SaslClientAuthenticationProviders;
import org.apache.hadoop.security.BytedanceUserProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

import static org.apache.hadoop.hbase.security.User.HBASE_SECURITY_CONF_KEY;

/**
 * <AUTHOR>
 */
@Configuration
public class HBaseConfig {

    @Autowired
    private HBaseProperties hBaseProperties;

    @Bean
    public org.apache.hadoop.conf.Configuration hbaseConfiguration() {
        org.apache.hadoop.conf.Configuration conf = HBaseConfiguration.create();
        conf.set(HConstants.ZOOKEEPER_QUORUM, hBaseProperties.getZkQuorum());
        conf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, hBaseProperties.getZkZoneParent());

        if (Strings.isNullOrEmpty(hBaseProperties.getUsername())) {
            // hbase 鉴权相关配置
            conf.setStrings(SaslClientAuthenticationProviders.EXTRA_PROVIDERS_KEY,
                PlainSaslClientAuthenticationProvider.class.getName());
            conf.set(SaslClientAuthenticationProviders.SELECTOR_KEY, AuthProviderSelector.class.getName());
            conf.set("hbase.client.userprovider.class", BytedanceUserProvider.class.getName());
            conf.set(HBASE_SECURITY_CONF_KEY, BytedanceUserProvider.BYTE_DANCE_AUTHENTICATION);
            conf.set(BytedanceUserProvider.HBASE_CLIENT_USERNAME, hBaseProperties.getUsername());
            conf.set(BytedanceUserProvider.HBASE_CLIENT_PASSWORD, hBaseProperties.getPassword());
        }

        return conf;
    }

    @Bean
    public Connection hbaseConnection(org.apache.hadoop.conf.Configuration hbaseConfiguration) throws IOException {
        return ConnectionFactory.createConnection(hbaseConfiguration);
    }

}
