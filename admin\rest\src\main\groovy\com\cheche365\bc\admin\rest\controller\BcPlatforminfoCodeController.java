package com.cheche365.bc.admin.rest.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.admin.rest.controller.base.BaseController;
import com.cheche365.bc.entity.mysql.BcPlatforminfoCode;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.service.BcPlatforminfoCodeService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

import static com.cheche365.bc.constants.Constants.PLATFORM_INFO_CODE_CACHE_KEY_PREFIX;
import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * <AUTHOR> href="<EMAIL>">wangyang</a>
 * @since 2021-02-23
 */
@RestController
@RequestMapping(value = "/platformInfoCode")
@AllArgsConstructor
public class BcPlatforminfoCodeController extends BaseController<BcPlatforminfoCodeService, BcPlatforminfoCode> {

    private final BcPlatforminfoCodeService bcPlatforminfoCodeService;
    private final StringRedisTemplate redisTemplate;

    /**
     * 平台信息码表-列表
     *
     * @param map            请求参数
     * @param authentication 认证信息
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.PLATFORM_INFO_CODE + COMMA + PermissionCode.PLATFORM_INFO_CODE_GETPAGE + SUFFIX)
    @Override
    @RequestMapping(value = "", method = RequestMethod.GET)
    public RestResponse getPage(@RequestParam Map<String, String> map, Authentication authentication) {
        return super.getPage(map, authentication);
    }

    /**
     * 平台信息码表-新增
     *
     * @param bcPlatforminfoCode 请求参数
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.PLATFORM_INFO_CODE + COMMA + PermissionCode.PLATFORM_INFO_CODE_SAVE + SUFFIX)
    @RequestMapping(value = "save", method = RequestMethod.POST)
    public RestResponse save(@RequestBody BcPlatforminfoCode bcPlatforminfoCode) {
        boolean exist = bcPlatforminfoCodeService.exist(bcPlatforminfoCode);
        if (exist) {
            return RestResponse.failedMessage("平台信息码表已存在，创建操作失败！");
        }

        if (bcPlatforminfoCodeService.save(bcPlatforminfoCode)) {
            JSONObject message = new JSONObject();
            message.put("operate", "add");
            message.put("bcPlatforminfoCode", JSONObject.toJSON(bcPlatforminfoCode));
            redisTemplate.convertAndSend(PLATFORM_INFO_CODE_CACHE_KEY_PREFIX, message.toJSONString());
            return RestResponse.successMessage("创建操作成功！");
        } else {
            return RestResponse.failedMessage("创建操作失败！");
        }

    }

    /**
     * 平台信息码表-更新
     *
     * @param bcPlatforminfoCode 请求参数
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.PLATFORM_INFO_CODE + COMMA + PermissionCode.PLATFORM_INFO_CODE_UPDATE + SUFFIX)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public RestResponse updateErrorCategory(@RequestBody BcPlatforminfoCode bcPlatforminfoCode) {
        if (null == bcPlatforminfoCodeService.getById(bcPlatforminfoCode.getId())) {
            return RestResponse.failedMessage("平台信息码表 ID 错误，更新操作失败！");
        }

        boolean exist = bcPlatforminfoCodeService.exist(bcPlatforminfoCode);
        if (exist) {
            return RestResponse.failedMessage("平台信息码表已存在，更新操作失败！");
        }

        if (bcPlatforminfoCodeService.updateById(bcPlatforminfoCode)) {
            JSONObject message = new JSONObject();
            message.put("operate", "update");
            redisTemplate.convertAndSend(PLATFORM_INFO_CODE_CACHE_KEY_PREFIX, message.toJSONString());
            return RestResponse.successMessage("更新操作成功！");
        } else {
            return RestResponse.failedMessage("更新操作失败！");
        }

    }

    /**
     * 平台信息码表-删除
     *
     * @param id 平台信息码表 ID
     * @return RestResponse
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.PLATFORM_INFO_CODE + COMMA + PermissionCode.PLATFORM_INFO_CODE_DELETE + SUFFIX)
    @GetMapping("delete/{id}")
    public RestResponse delete(@PathVariable Long id) {
        BcPlatforminfoCode bcPlatforminfoCode = bcPlatforminfoCodeService.getById(id);
        if (null == bcPlatforminfoCode) {
            return RestResponse.failedMessage("平台信息码表 ID 错误，删除操作失败！");
        }

        if (bcPlatforminfoCodeService.removeById(id)) {
            JSONObject message = new JSONObject();
            message.put("operate", "delete");
            message.put("bcPlatforminfoCode", JSON.toJSON(bcPlatforminfoCode));
            redisTemplate.convertAndSend(PLATFORM_INFO_CODE_CACHE_KEY_PREFIX, message.toJSONString());
            return RestResponse.successMessage("删除操作成功！");
        } else {
            return RestResponse.failedMessage("删除操作失败！");
        }

    }

}
