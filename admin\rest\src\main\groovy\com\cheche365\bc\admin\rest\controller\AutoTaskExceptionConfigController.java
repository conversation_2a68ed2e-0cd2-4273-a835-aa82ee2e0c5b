package com.cheche365.bc.admin.rest.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cheche365.bc.admin.service.constant.MenuCode;
import com.cheche365.bc.admin.service.constant.PermissionCode;
import com.cheche365.bc.dto.AutoTaskExceptionConfigAddReqDto;
import com.cheche365.bc.dto.AutoTaskExceptionConfigEditReqDto;
import com.cheche365.bc.dto.AutoTaskExceptionConfigPageReqDto;
import com.cheche365.bc.dto.AutoTaskExceptionConfigPageResDto;
import com.cheche365.bc.entity.mysql.AutoTaskExceptionConfig;
import com.cheche365.bc.entity.mysql.UserInfo;
import com.cheche365.bc.model.RestResponse;
import com.cheche365.bc.admin.service.service.UserInfoService;
import com.cheche365.bc.service.AutoTaskExceptionConfigService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.cheche365.bc.admin.service.constant.SymbolConstants.*;

/**
 * 百川异常配置
 *
 * <AUTHOR> href="<EMAIL>>wangyang</a>
 * @since 2021-12-16
 */
@RestController
@RequestMapping(value = "autoTaskExceptionConfigs")
@AllArgsConstructor
public class AutoTaskExceptionConfigController {

    private final AutoTaskExceptionConfigService autoTaskExceptionConfigService;
    private final UserInfoService userInfoService;
    /**
     * 列表
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_EXCEPTION + COMMA +
            PermissionCode.AUTOTASK_EXCEPTION_GETPAGE + SUFFIX)
    @GetMapping(value = "")
    public RestResponse<Page<AutoTaskExceptionConfigPageResDto>> page(
            AutoTaskExceptionConfigPageReqDto reqDto, Authentication authentication) {
        Integer currentPage = reqDto.getCurrentPage();
        Integer pageSize = reqDto.getPageSize();
        Page<AutoTaskExceptionConfig> page = new Page<>(currentPage, pageSize);
        QueryWrapper<AutoTaskExceptionConfig> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<AutoTaskExceptionConfig> lambda = queryWrapper.lambda();
        if (StringUtils.isNotBlank(reqDto.getCompanyId())) {
            lambda.eq(AutoTaskExceptionConfig::getCompanyId, reqDto.getCompanyId());
        }

        if (StringUtils.isNotBlank(reqDto.getProcessType())) {
            lambda.eq(AutoTaskExceptionConfig::getProcessType, reqDto.getProcessType());
        }

        if (StringUtils.isNotBlank(reqDto.getTaskType())) {
            lambda.eq(AutoTaskExceptionConfig::getTaskType, reqDto.getTaskType());
        }

        if (StringUtils.isNotBlank(reqDto.getExceptionKeywords())) {
            lambda.like(AutoTaskExceptionConfig::getExceptionKeywords,
                    "%" + reqDto.getExceptionKeywords() + "%");
        }

        if (StringUtils.isNotBlank(reqDto.getExceptionConversion())) {
            lambda.like(AutoTaskExceptionConfig::getExceptionConversion,
                    "%" + reqDto.getExceptionConversion() + "%");
        }

        if (reqDto.getOperatorId() != null) {
            lambda.eq(AutoTaskExceptionConfig::getOperatorId, reqDto.getOperatorId());
        }

        queryWrapper.lambda().orderByDesc(AutoTaskExceptionConfig::getCreateTime)
                .orderByDesc(AutoTaskExceptionConfig::getUpdateTime);
        autoTaskExceptionConfigService.page(page, queryWrapper);

        List<UserInfo> list = userInfoService.list(new QueryWrapper<UserInfo>().lambda()
                .select(UserInfo::getId, UserInfo::getName));
        Map<Integer, String> userNameMap = new HashMap<>();
        if (list.size() > 0) {
            userNameMap = list.stream().collect(Collectors.toMap(
                    userInfo -> userInfo.getId().intValue(), UserInfo::getName));
        }
        // 封装响应实体
        List<AutoTaskExceptionConfigPageResDto> resDtoList = new ArrayList<>();
        for (AutoTaskExceptionConfig config : page.getRecords()) {
            AutoTaskExceptionConfigPageResDto resDto = new AutoTaskExceptionConfigPageResDto();
            BeanUtils.copyProperties(config, resDto);
            resDto.setOperatorName(userNameMap.getOrDefault(config.getOperatorId(), ""));
            resDtoList.add(resDto);
        }

        Page<AutoTaskExceptionConfigPageResDto> p = new Page<>();
        p.setCurrent(currentPage);
        p.setSize(pageSize);
        p.setTotal(page.getTotal());
        p.setPages(page.getPages());
        p.setRecords(resDtoList);
        return RestResponse.success(p);
    }

    /**
     * 详情
     */
    @GetMapping(value = "{id}")
    public RestResponse<AutoTaskExceptionConfig> getOne(@PathVariable("id") Long id) {
        AutoTaskExceptionConfig AutoTaskExceptionConfig = autoTaskExceptionConfigService.getById(id);
        return RestResponse.success(AutoTaskExceptionConfig);
    }

    /**
     * 新增
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_EXCEPTION + COMMA +
            PermissionCode.AUTOTASK_EXCEPTION_ADD + SUFFIX)
    @PostMapping(value = "")
    public RestResponse<String> save(@RequestBody AutoTaskExceptionConfigAddReqDto reqDto) {
        boolean save;
        AutoTaskExceptionConfig exist = autoTaskExceptionConfigService.getOne(
                new QueryWrapper<AutoTaskExceptionConfig>().lambda()
                        .eq(AutoTaskExceptionConfig::getCompanyId, reqDto.getCompanyId())
                        .eq(AutoTaskExceptionConfig::getProcessType, reqDto.getProcessType())
                        .eq(AutoTaskExceptionConfig::getTaskType, reqDto.getTaskType())
                        .eq(AutoTaskExceptionConfig::getExceptionKeywords, reqDto.getExceptionKeywords())
                        .eq(AutoTaskExceptionConfig::getOperatorId, reqDto.getOperatorId()));
        if (!Objects.isNull(exist)) {
            RestResponse<String> failed = RestResponse.failed("");
            failed.setRestMsg("异常配置信息重复");
            return failed;
        }

        AutoTaskExceptionConfig AutoTaskExceptionConfig = new AutoTaskExceptionConfig();
        BeanUtils.copyProperties(reqDto, AutoTaskExceptionConfig);
        save = autoTaskExceptionConfigService.save(AutoTaskExceptionConfig);
        if (save) {
            return RestResponse.success("创建成功");
        } else {
            RestResponse<String> failed = RestResponse.failed("");
            failed.setRestMsg("创建失败");
            return failed;
        }
    }

    /**
     * 修改
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_EXCEPTION + COMMA +
            PermissionCode.AUTOTASK_EXCEPTION_UPDATE + SUFFIX)
    @PutMapping(value = "{id}")
    public RestResponse<String> update(@PathVariable("id") Integer id,
                                       @RequestBody AutoTaskExceptionConfigEditReqDto reqDto) {
        AutoTaskExceptionConfig exist = autoTaskExceptionConfigService.getById(id);
        if (Objects.isNull(exist)) {
            RestResponse<String> failed = RestResponse.failed("");
            failed.setRestMsg("异常配置信息不存在");
            return failed;
        }

        List<AutoTaskExceptionConfig> existList = autoTaskExceptionConfigService.list(
                new QueryWrapper<AutoTaskExceptionConfig>().lambda()
                        .ne(AutoTaskExceptionConfig::getId, id)
                        .eq(AutoTaskExceptionConfig::getCompanyId, reqDto.getCompanyId())
                        .eq(AutoTaskExceptionConfig::getProcessType, reqDto.getProcessType())
                        .eq(AutoTaskExceptionConfig::getTaskType, reqDto.getTaskType())
                        .eq(AutoTaskExceptionConfig::getExceptionKeywords, reqDto.getExceptionKeywords())
                        .eq(AutoTaskExceptionConfig::getOperatorId, reqDto.getOperatorId())
        );
        if (existList.size() > 0) {
            RestResponse<String> failed = RestResponse.failed("");
            failed.setRestMsg("异常配置信息重复");
            return failed;
        }

        AutoTaskExceptionConfig config = new AutoTaskExceptionConfig();
        config.setId(id);
        BeanUtils.copyProperties(reqDto, config);
        autoTaskExceptionConfigService.updateById(config);
        return RestResponse.success("更新成功");
    }

    /**
     * 删除
     */
    @PreAuthorize(HAS_PERMISSION_PREFIX + MenuCode.AUTOTASK_EXCEPTION + COMMA +
            PermissionCode.AUTOTASK_EXCEPTION_DELETE + SUFFIX)
    @DeleteMapping(value = "{id}")
    public RestResponse<String> delete(@PathVariable Long id) {
        autoTaskExceptionConfigService.removeById(id);
        return RestResponse.success("删除成功");
    }

}
