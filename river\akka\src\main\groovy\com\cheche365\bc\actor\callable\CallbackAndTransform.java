package com.cheche365.bc.actor.callable;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.cheche365.bc.exception.TaskException;
import com.cheche365.bc.service.*;
import com.cheche365.bc.task.AutoTask;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.Callable;

import static com.cheche365.bc.cache.RedisCache.getStringRedis;
import static com.cheche365.bc.constants.Constants.CLUE_PUSH;

/**
 * 1. 回调
 * 2. 日志与错误处理
 * 3. 数据转换
 *
 * <AUTHOR>
 */
@Slf4j
public class CallbackAndTransform implements Callable<AutoTask> {
    final AutoTask autoTask;

    private final AutoTaskService autoTaskService;

    private final TaskSyncService taskSyncService;

    private final ErrorHandlingService errorHandlingService;

    private final TransformService transformService;

    private final CallbackService callbackService;

    public CallbackAndTransform(
        AutoTask autoTask,
        AutoTaskService autoTaskService,
        ErrorHandlingService errorHandlingService,
        TaskSyncService taskSyncService,
        TransformService transformService,
        CallbackService callbackService
    ) {
        this.autoTask = autoTask;
        this.autoTaskService = autoTaskService;
        this.errorHandlingService = errorHandlingService;
        this.taskSyncService = taskSyncService;
        this.transformService = transformService;
        this.callbackService = callbackService;
    }

    @Override
    public AutoTask call() throws Exception {
        try {

            // 无回调地址
            if (Strings.isNullOrEmpty(autoTask.getCallBackUrl())) {
                autoTask.setResultStr(Strings.nullToEmpty(autoTask.getResultStr()).concat("无回调地址不回调!"));
                autoTaskService.updateById(autoTask);
                return autoTask;
            }

            // 错误消息映射
            errorHandlingService.handleTaskError(autoTask);

            if (ObjectUtil.isNull(autoTask.getTaskEntity())) {
                log.warn("无任务数据不回写:{}", autoTask.getTaskId());
                autoTask.setResultStr(String.format("无任务数据不回写:%s", autoTask.getTaskId()));
                //保存
                autoTaskService.updateById(autoTask);
                return autoTask;
            }

            //生成回写信息
            transformService.transformResponseData(autoTask);
            //任务数据回写
            String result = taskSyncService.callbackData(autoTask);
            //回写数据整理
            callbackService.handle(autoTask, result);
            //保存
            autoTaskService.updateById(autoTask);

            Boolean clueFlag = (Boolean) autoTask.getTempValues().get("clueFlag");
            if (Objects.nonNull(clueFlag) && clueFlag) {
                Objects.requireNonNull(getStringRedis()).convertAndSend(CLUE_PUSH, JSONUtil.toJsonStr(autoTask));
            }
        } catch (Exception e) {
            log.error("任务:{}回写过程出现异常:", autoTask.getTaskId(), e);
            autoTask.setResultStr(String.format("回写的平台信息为：%s\n回写过程异常:%s,%s", autoTask.getPlatformInfo(), e.getMessage(), autoTask.getIsReserved()));
            //保存
            try {
                autoTaskService.updateById(autoTask);
                throw new TaskException(autoTask, e.getMessage(), e);
            } catch (Exception eSub) {
                throw new TaskException(autoTask, eSub.getMessage(), eSub);
            }
        }
        return autoTask;
    }

}
