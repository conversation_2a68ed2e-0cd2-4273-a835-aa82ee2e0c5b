package com.cheche365.bc.hbase;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.Admin;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.regionserver.BloomType;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;

/**
 * HBase表创建工具类
 * 用于创建auto_task_log和action_log表
 */
public class HBaseTableCreator {

    public static void main(String[] args) {
        Configuration conf = HBaseConfiguration.create();
        conf.set(HConstants.ZOOKEEPER_QUORUM, "192.168.1.133:2181");
        conf.set(HConstants.ZOOKEEPER_ZNODE_PARENT, "/hbase");

        try (Connection connection = ConnectionFactory.createConnection(conf);
            Admin admin = connection.getAdmin()) {
            // 删除表
            deleteTable(admin, "auto_task_log");
            deleteTable(admin, "action_log");
            // 创建auto_task_log表
            createAutoTaskLogTable(admin);
//            createAutoTaskLogTableTest(admin);

            // 创建action_log表
            createActionLogTable(admin);



            System.out.println("HBase表操作完成！");

        } catch (IOException e) {
            System.err.println("创建HBase表时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建auto_task_log表
     */
    private static void createAutoTaskLogTableTest(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_log_test");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(infoColumn);
        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_log");
    }

    /**
     * 删除HBase表
     */
    private static void deleteTable(Admin admin, String tableNameStr) throws IOException {
        TableName tableName = TableName.valueOf(tableNameStr);
        if (admin.tableExists(tableName)) {
            admin.disableTable(tableName);
            admin.deleteTable(tableName);
            System.out.println("成功删除表: " + tableNameStr);
        } else {
            System.out.println("表 " + tableNameStr + " 不存在，跳过删除");
        }
    }

    /**
     * 创建auto_task_log表
     */
    private static void createAutoTaskLogTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("auto_task_log");

        if (admin.tableExists(tableName)) {
            System.out.println("表 auto_task_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(infoColumn);
        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒

        tableDescriptor.addFamily(dataColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: auto_task_log");
    }

    /**
     * 创建action_log表
     */
    private static void createActionLogTable(Admin admin) throws IOException {
        TableName tableName = TableName.valueOf("action_log");

        if (admin.tableExists(tableName)) {
            System.out.println("表 action_log 已存在，跳过创建");
            return;
        }

        HTableDescriptor tableDescriptor = new HTableDescriptor(tableName);

        // 创建info列族
        HColumnDescriptor infoColumn = new HColumnDescriptor("info");
        infoColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        infoColumn.setBloomFilterType(BloomType.ROW);
        infoColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(infoColumn);

        // 创建data列族
        HColumnDescriptor dataColumn = new HColumnDescriptor("data");
        dataColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        dataColumn.setBloomFilterType(BloomType.ROW);
        dataColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(dataColumn);

        // 创建error列族
        HColumnDescriptor errorColumn = new HColumnDescriptor("error");
        errorColumn.setCompressionType(Compression.Algorithm.SNAPPY);
        errorColumn.setBloomFilterType(BloomType.ROW);
        errorColumn.setTimeToLive(7776000); // TTL 7776000秒
        tableDescriptor.addFamily(errorColumn);

        // 设置预分区
        byte[][] splits = new byte[][]{
            Bytes.toBytes("1"),
            Bytes.toBytes("2"),
            Bytes.toBytes("3"),
            Bytes.toBytes("4"),
            Bytes.toBytes("5"),
            Bytes.toBytes("6"),
            Bytes.toBytes("7"),
            Bytes.toBytes("8"),
            Bytes.toBytes("9")
        };

        admin.createTable(tableDescriptor, splits);
        System.out.println("成功创建表: action_log");
    }
}
