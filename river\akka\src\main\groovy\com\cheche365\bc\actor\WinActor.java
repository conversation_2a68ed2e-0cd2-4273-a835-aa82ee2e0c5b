package com.cheche365.bc.actor;

import akka.actor.ActorSelection;
import akka.actor.Cancellable;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.cheche365.bc.actor.msg.RefreshMsg;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.actor.msg.WinCloseMsg;
import com.cheche365.bc.actor.msg.WinStateMsg;
import com.cheche365.bc.cache.RedisCache;
import com.cheche365.bc.exception.ExceptionCde;
import com.cheche365.bc.exception.InsReturnException;
import com.cheche365.bc.exception.UniqueException;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.tools.DateUtil;
import com.cheche365.bc.tools.StringUtil;
import com.cheche365.bc.utils.AkkaTaskUtil;
import com.cheche365.bc.utils.sender.HttpSender;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.openqa.selenium.WebDriver;
import org.springframework.data.redis.core.StringRedisTemplate;
import scala.concurrent.duration.Duration;

import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.cheche365.bc.constants.Constants.LOGIN_FAIL_ACCOUNTS;
import static com.cheche365.bc.constants.Constants.SESSION_CACHE_KEY_PREFIX;
import static com.cheche365.bc.utils.AkkaTaskUtil.populateTransTaskErrorMsg;
import static com.cheche365.bc.utils.sender.HttpSender.buildHttpClient;

/**
 * 账号开放的窗口Actor
 * 窗口只会一个一个任务处理,类似叫号机,暂时只供精灵使用，EDI有socket，axis2等暂时不支持
 *
 * - 执行具体任务
 * - 保持会话
 * - 错误重试
 * <AUTHOR>
 * @Created by austinChen on 2017/4/17 16:33.
 */

@Getter
@Setter
public class WinActor extends BaseActor {

    private static final Set<String> SKIP_HOSTS = Sets.newHashSet("autopp.tpi.cntaiping.com", "wwcd.sinosig.com");

    /**
     * 窗口编号
     */
    private int no;

    /**
     * Actor的路径
     */
    private String path;

    /**
     * 需要保持的httpClient
     */
    private CloseableHttpClient closeableHttpClient = null;
    private LoginConfig loginConfig;

    private AtomicInteger successTimes = new AtomicInteger(0);
    private AtomicInteger failTimes = new AtomicInteger(0);
    private String taskKey = "";
    private boolean showRefreshContent = false;
    private String account = "";
    /**
     * 刷新成功率统计
     * index_0:刷新次数
     * index_1:刷新成功次数
     */
    private int failureTimes = 0;

    /**
     * This will schedule to send the Tick-message
     */
    Cancellable cancellable;

    private WebDriver driver = null;

    private StringRedisTemplate redisTemplate = RedisCache.getStringRedis();


    public WinActor(int no, LoginConfig loginConfig, String account) {
        this.no = no;
        this.account = account;
        this.actorKey = account + "-" + no;
        this.loginConfig = loginConfig;
        initCloseableHttpClient();
        cancellable = context().system().scheduler().schedule(Duration.Zero(),
                Duration.create(loginConfig.getRefreshMin(), TimeUnit.MINUTES), self(), RefreshMsg.getInstance(),
                context().system().dispatcher(), null);
    }

    private void initCloseableHttpClient() {
        log.info("WinActor 初始化 HttpClient 参数UseNewSSL：{}", loginConfig.getUseNewSSL());
        if (loginConfig.getUseNewSSL()) {
            try {
                String refreshUrl = loginConfig.getRefreshUrl();
                log.info("WinActor 初始化 HttpClient 参数refreshUrl：{}", refreshUrl);
                if (StrUtil.isNotBlank(refreshUrl) && SKIP_HOSTS.contains(URLUtil.url(refreshUrl).getHost())) {
                    log.info("WinActor 初始化HttpClient，跳过TLSv1.2类型");
                    closeableHttpClient = buildHttpClient(HttpSender.getSSLSocketFactoryNoCheck());
                } else {
                    closeableHttpClient = buildHttpClient(HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}));
                }
            } catch (Exception e) {
                log.error("WinActor初始化TLSv1.2类型HttpClient失败：{}", ExceptionUtils.getStackTrace(e));
                closeableHttpClient = buildHttpClient(loginConfig.getUsePoolConnect());
            }
        } else {
            closeableHttpClient = buildHttpClient(loginConfig.getUsePoolConnect());
        }
    }

    public void processTask(TransTaskMsg transTaskMsg) throws Exception {
        AkkaTaskUtil.process(transTaskMsg.getItf(), transTaskMsg.getAutoTask());
    }

    private void closePreConnection() {
        if (closeableHttpClient != null) {
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                log.error(this.getTaskKey() + "关闭上一个链接失败", e);
            }
        }
        if (driver != null) {
            driver.quit();
        }
    }

    @Override
    public void onReceive(Object msg) throws Exception {
        try {
            if (msg instanceof TransTaskMsg transTaskMsg) {
                try {
                    taskKey = transTaskMsg.getUniqueKey();
                    this.loginConfig = transTaskMsg.getLoginConfig();
                    AutoTask autoTask = transTaskMsg.getAutoTask();
                    if (autoTask.getDriver() == null) {
                        autoTask.setDriver(driver);
                    }
                    Map configMap = autoTask.getConfigs();
                    String taskId = autoTask.getTempValues().get("enquiryId").toString();
                    //某些公司如人保四代，登录后所有请求头需要带token，登录后将token存redis，有效期自行决定，key规则：头:保司编号:登录账号
                    String token = redisTemplate.opsForValue().get(SESSION_CACHE_KEY_PREFIX + transTaskMsg.getItf().getComId() + ":" + autoTask.getConfigs().get("login"));
                    //只在提单调用的时候，传入任务号，定时刷新传空串
                    if (StringUtils.isNotBlank(token) || loginConfig.refresh(closeableHttpClient, showRefreshContent, taskId)) {
                        //已登录状态,系统将自动去掉登陆模板
                        transTaskMsg.getItf().getTemplates().remove(0);
                    } else {
                        this.closePreConnection();
                        if (configMap.containsKey(HttpSender.proxyHost)
                                && configMap.containsKey(HttpSender.proxyPort)
                                && configMap.containsKey(HttpSender.proxyName)
                                && configMap.containsKey(HttpSender.proxyPass)) {
                            closeableHttpClient = buildHttpClient(loginConfig.getUsePoolConnect(), configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()), configMap.get(HttpSender.proxyName).toString(), configMap.get(HttpSender.proxyPass).toString(), loginConfig.getUseNewSSL() ? HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}) : null);
                        } else {
                            //刷新失败也要关闭旧链接再次登陆。免得保险公司策略认为多次同链接登陆。没有注销，有异常行为
//                                closeableHttpClient = buildHttpClient(loginConfig.getUsePoolConnect());
                            initCloseableHttpClient();
                        }
                    }
                    autoTask.setHttpClient(closeableHttpClient);
                    String checkKey = LOGIN_FAIL_ACCOUNTS + this.account;
                    String dateNow = DateUtil.d2SMinute();
                    if (loginConfig.getQuoteLimit() > 0) {
                        redisTemplate.opsForValue().set("auto:account:limit:" + this.account + ":quote:" + dateNow, String.valueOf(loginConfig.getQuoteLimit()), 60, TimeUnit.SECONDS);
                    }
                    if (loginConfig.getAutoinsureLimit() > 0) {
                        redisTemplate.opsForValue().set("auto:account:limit:" + this.account + ":autoinsure:" + dateNow, String.valueOf(loginConfig.getAutoinsureLimit()), 60, TimeUnit.SECONDS);
                    }
                    if (loginConfig.getInsurequeryLimit() > 0) {
                        redisTemplate.opsForValue().set("auto:account:limit:" + this.account + ":insurequery:" + dateNow, String.valueOf(loginConfig.getInsurequeryLimit()), 60, TimeUnit.SECONDS);
                    }
                    if (loginConfig.getApprovedqueryLimit() > 0) {
                        redisTemplate.opsForValue().set("auto:account:limit:" + this.account + ":approvedquery:" + dateNow, String.valueOf(loginConfig.getApprovedqueryLimit()), 60, TimeUnit.SECONDS);
                    }
                    String currentFails = redisTemplate.opsForValue().get(checkKey);

                    for (int i = 0; i < 3; i++) {
                        try {
                            if (StringUtils.isNotBlank(currentFails)) {
                                int illegalCount = Integer.parseInt(currentFails);
                                if (illegalCount >= 10) {
                                    final String cause = String.format("账号【%s】非法登陆超过10次，不再进行操作,请纠正账号配置后联系运营人员(操作key:%s)", transTaskMsg.getAccount(), checkKey);
                                    transTaskMsg.getAutoTask().setResultStr(cause);
                                    transTaskMsg.getAutoTask().setFailureCause(cause);
                                    transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                                    failTimes.getAndIncrement();
                                    break;
                                }
                            }
                            processTask(transTaskMsg);
                            redisTemplate.opsForValue().set(checkKey, "0");
                            transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultSuccessStatus());
                            successTimes.getAndIncrement();
                            break;
                        } catch (UniqueException uex) {
                            if (uex.getCode() == UniqueException.IllegalAuth) {
                                if (StringUtil.isEmpty(currentFails)) {
                                    redisTemplate.opsForValue().set(checkKey, "0");
                                } else {
                                    log.info("业务窗口{}-{}窗口处理任务{}登陆授权失败，增加一次非法登陆次数", this.account, this.no, this.taskKey);
                                    redisTemplate.opsForValue().increment(checkKey);
                                }
                                transTaskMsg.getAutoTask().setResultStr(uex.getMessage());
                                transTaskMsg.getAutoTask().setFailureCause(uex.getMessage());
                                transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                                failTimes.getAndIncrement();
                                break;
                            } else if (uex.getCode() == UniqueException.IllegalSign) {
                                if (loginConfig != null) {
                                    if (configMap.containsKey(HttpSender.proxyHost)
                                            && configMap.containsKey(HttpSender.proxyPort)
                                            && configMap.containsKey(HttpSender.proxyName)
                                            && configMap.containsKey(HttpSender.proxyPass)) {
                                        this.closePreConnection();
                                        closeableHttpClient = buildHttpClient(true, configMap.get(HttpSender.proxyHost).toString(), Integer.parseInt(configMap.get(HttpSender.proxyPort).toString()), configMap.get(HttpSender.proxyName).toString(), configMap.get(HttpSender.proxyPass).toString(), loginConfig.getUseNewSSL() ? HttpSender.getSSlSocketFactory("TLSv1.2", new String[]{"TLSv1.2"}) : null);
                                    } else {
                                        this.closePreConnection();
//                                        closeableHttpClient = buildHttpClient(loginConfig.getUsePoolConnect());
                                        initCloseableHttpClient();
                                    }
                                    if (i < 2) {
                                        if (loginConfig.login(closeableHttpClient, transTaskMsg)) {
                                            if (transTaskMsg.getItf().getTemplates().get(0).getName().contains("login")) {
                                                //登录成功之后如果 之前没有跳过登录模板 则去掉登录模板继续执行
                                                transTaskMsg.getItf().getTemplates().remove(0);
                                            }
                                            continue;
                                        }
                                        // 此处不做setResultStr和setFailureCause处理，是因为上面login()里面做了
                                    } else {
                                        populateTransTaskErrorMsg(transTaskMsg, uex);
                                    }
                                    log.info("业务窗口{}-{}窗口处理任务{}辅助登陆失败", this.account, this.no, this.taskKey);
                                    transTaskMsg.getAutoTask().setEndFlag(true);
                                    transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                                    break;
                                }
                            }
                            populateTransTaskErrorMsg(transTaskMsg, uex);
                            transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                            failTimes.getAndIncrement();
                            break;
                        } catch (Exception e) {
                            String cause = Strings.isNullOrEmpty(e.getMessage()) ? e.toString() : e.getMessage();
                            transTaskMsg.getAutoTask().setResultStr(cause);
                            transTaskMsg.getAutoTask().setFailureCause(cause);
                            if ((!transTaskMsg.getAutoTask().getErrorInfo().containsKey("errorcode")) || transTaskMsg.getAutoTask().getErrorInfo().get("errorcode") == null) {
                                if (autoTask.getCompanyId().startsWith("2005") && autoTask.getTempValues().containsKey("isPaymentDept")) {
                                    //人保承保查询
                                    autoTask.getErrorInfo().put("errordesc", cause);
                                    autoTask.getErrorInfo().put("errorcode", ExceptionCde.APPROVED_QUERY_PAY_SURE_FAIL.getCde());
                                    autoTask.getErrorInfo().put("excepNotiPhoneNum", autoTask.getConfigs().get("excepNotiPhoneNum"));
                                } else {
                                    transTaskMsg.getAutoTask().getErrorInfo().put("errorcode", ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                                    transTaskMsg.getAutoTask().getErrorInfo().put("errordesc", cause);
                                }
                            }
                            if (transTaskMsg.getAutoTask().getTaskStatus() != null && !"".equals(transTaskMsg.getAutoTask().getTaskStatus())) {
                                transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getAutoTask().getTaskStatus());
                            } else {
                                transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                            }
                            failTimes.getAndIncrement();
                            if (!(e instanceof InsReturnException)) {
                                log.error("业务窗口" + this.account + "-" + this.no + " 异常：", ExceptionUtils.getStackTrace(e));
                            }
                            break;
                        }
                    }
                    transTaskMsg.getAutoTask().setEndFlag(true);
                    ActorSelection taskActor = context().system().actorSelection("akka://sys/user/taskActor");
                    taskActor.tell(transTaskMsg.getAutoTask(), self());
                    //处理完毕把当前任务至空
                    this.freeWin();

                } catch (Throwable e) {
                    log.error("业务窗口" + this.account + "-" + this.no + "Actor处理任务:{}@{}出现异常！", transTaskMsg.getAutoTask().getTraceKey(), transTaskMsg.getAutoTask().getCompanyId(), e);
                    transTaskMsg.getAutoTask().setEndFlag(true);
                    transTaskMsg.getAutoTask().setResultFlag(false);
                    transTaskMsg.getAutoTask().setResultStr(e.getMessage());
                    transTaskMsg.getAutoTask().setFailureCause(e.getMessage());
                    transTaskMsg.getAutoTask().setTaskStatus(transTaskMsg.getItf().getDefaultFailedStatus());
                    ActorSelection taskActor = context().system().actorSelection("akka://sys/user/taskActor");
                    taskActor.tell(transTaskMsg.getAutoTask(), self());
                    //处理完毕把当前任务至空
                    failTimes.incrementAndGet();
                    this.freeWin();
                }
            } else if (msg instanceof RefreshMsg) {
                failureTimes++;
                if (loginConfig.refresh(closeableHttpClient, showRefreshContent, "")) {
                    failureTimes = 0;
                }
                //连续5次保持会话刷新失败，通知父actor关闭当前窗口
                if (failureTimes >= 5) {
                    //保持会话窗口将关闭，通知父窗口
                    context().lookupRoot().getParent().tell(WinCloseMsg.getInstance(), self());
                    log.info("业务窗口:{}-{}健康度低于0.2,即将回收处理", this.account, this.no);
                }
            } else if (msg instanceof LoginConfig) {
                this.loginConfig = (LoginConfig) msg;
            } else {
                log.error("业务窗口{}-{}窗口Actor收到未知的消息{}", this.account, this.no, msg);
            }
        } catch (Throwable ex) {
            log.error("业务窗口" + this.account + "-" + this.account + "Actor处理消息出现异常！", ex);
            this.freeWin();
        }
    }

    private void freeWin() {
        log.info("业务窗口{}-{}窗口置闲", this.account, this.no);
        this.taskKey = "";
        context().parent().tell(new WinStateMsg(this.getTaskKey(), successTimes.get(), failTimes.get()), self());
        //this.closePreConnection();
        log.info("业务窗口{}-{}完成任务成功{}，失败{}", this.account, no, successTimes, failTimes);
    }

    @Override
    public String toString() {
        return "窗口:" + this.account + "-" + String.valueOf(no);
    }


    @Override
    public void preStart() throws Exception {
        log.info("业务窗口{}-{}启动", this.account, this.no);
        super.preStart();
    }

    @Override
    public void postStop() throws Exception {
        this.closePreConnection();
        log.info("业务窗口{}-{}停止", this.account, this.no);
        context().parent().tell(WinCloseMsg.getInstance(), self());
        cancellable.cancel();
        super.postStop();
    }

}

