package com.cheche365.bc.utils;

import akka.actor.ActorRef;
import akka.actor.ActorSelection;
import akka.actor.ActorSystem;
import akka.actor.PoisonPill;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.cheche365.bc.actor.BaseActor;
import com.cheche365.bc.actor.msg.TransTaskMsg;
import com.cheche365.bc.cache.RedisCache;
import com.cheche365.bc.entity.hbase.ActionLog;
import com.cheche365.bc.entity.mysql.Interface;
import com.cheche365.bc.entity.mysql.Template;
import com.cheche365.bc.exception.*;
import com.cheche365.bc.handler.BaseHandler;
import com.cheche365.bc.hbase.ActionLogRepository;
import com.cheche365.bc.message.TaskStatus;
import com.cheche365.bc.model.car.Enquiry;
import com.cheche365.bc.service.BcPlatforminfoCodeService;
import com.cheche365.bc.service.InterfaceService;
import com.cheche365.bc.service.impl.BcPlatforminfoCodeServiceImpl;
import com.cheche365.bc.sharding.OldTableSwitch;
import com.cheche365.bc.task.AutoTask;
import com.cheche365.bc.task.ErrorInfoKeys;
import com.cheche365.bc.tools.MapUtil;
import com.cheche365.bc.util.ScriptUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cheche365.bc.constants.Constants.SESSION_CACHE_KEY_PREFIX;
import static com.cheche365.bc.utils.TaskUtil.useVpn;

/**
 * 任务工具
 *
 * <AUTHOR>
 * @Created by austinChen on 2016/12/13 13:22.
 */
@Component
@Slf4j
public final class AkkaTaskUtil {

    @Autowired
    private MongoTemplate mongoTemplate;
    private static MongoTemplate mongoRepository;

    @Autowired
    private ActionLogRepository actionLogRepository;

    private static ActionLogRepository hbaseActionLogRepository;

    @Autowired
    private InterfaceService interfaceService;
    @Autowired
    private ActorSystem actorSystem;
    private static InterfaceService staticInterfaceService;
    private static ActorSystem staticActorSystem;
    private static Pattern urlPattern = Pattern.compile("\\$(\\{(\\w+.)*\\})");
    private BcPlatforminfoCodeService bcPlatforminfoCodeService;

    private static BcPlatforminfoCodeService staticBcPlatforminfoCodeService;

    @Autowired
    public void setBcPlatforminfoCodeService(BcPlatforminfoCodeService bcPlatforminfoCodeService) {
        this.bcPlatforminfoCodeService = bcPlatforminfoCodeService;
    }


    @PostConstruct
    public void init() {
        mongoRepository = mongoTemplate;
        staticInterfaceService = interfaceService;
        staticActorSystem = actorSystem;
        staticBcPlatforminfoCodeService = bcPlatforminfoCodeService;
        hbaseActionLogRepository = actionLogRepository;
    }

    /**
     * @param temp     模板
     * @param autoTask 单个任务
     * @return 任务
     * @throws Exception
     * @parma actionLog 记录动作日志
     */
    public static AutoTask process(Interface itf, Template temp, AutoTask autoTask, ActionLog actionLog) throws Exception {
        if (autoTask == null || temp == null) {
            return autoTask;
        }
        actionLog.setAutoTraceId(autoTask.getAutoTraceId());
        actionLog.setActionName(temp.getName());

        String taskId = autoTask.getTempValues().get("enquiryId").toString();
        Map<String, Object> inActionData = new HashMap<>();
        Map<String, Object> outActionData = new HashMap<>();
        String requestBody = null;
        String responseBody = null;

        try {
            //请求数据处理
            requestBody = initRequestBody(autoTask, inActionData, actionLog, itf, temp);
        } catch (TempSkipException skipE) {
            actionLog.setReceiveTime(DateTime.now());
            if (skipE.getMinSteps() > 0 && (itf.getTemplates().size() - skipE.getMinSteps()) > itf.getTempSkippedTimes()) {
                log.warn("任务{}不符合{}模板执行条件，该模板将被跳过", taskId, temp.getName());
                itf.setTempSkippedTimes(itf.getTempSkippedTimes() + 1);
                actionLog.setExceptionInfo(String.format("任务%s由于:%s原因，该模板将被跳过", taskId, skipE.getMessage()));
                saveActionLog(autoTask, actionLog);
                return autoTask;
            } else {
                log.error("任务:{}当前模板{}需要跳过执行,但是不满足跳过条件!", taskId, temp.getName());
                actionLog.setExceptionInfo(String.format("任务:%s当前模板%s需要跳过执行,但是不满足跳过条件!", taskId, temp.getName()));
                saveActionLog(autoTask, actionLog);
                throw skipE;
            }
        }

        String realUrl = null;
        if (!"2".equals(autoTask.getConfigs().get("robotType"))) {
            //获取发送请求的url
            realUrl = initUrl(autoTask, inActionData, itf, temp);
            log.info("任务:{},接口:{}调用开始,访问地址:{}", taskId, temp.getName(), realUrl);
            CloseableHttpClient closeableHttpClient = null;
            if (autoTask.getHttpClient() != null) {
                closeableHttpClient = (CloseableHttpClient) autoTask.getHttpClient();
            }
            actionLog.setUrl(realUrl);
            actionLog.setSendTime(new Date());
            try {
                //只有2072北部湾财险需要这个当前模板名称
                autoTask.getTempValues().put("curTemplateName", temp.getName());
                //根据保司编号匹配发送请求逻辑
                BaseHandler handler = TaskUtil.requestMethodMap.get(temp.getRequestChannel());
                if (Objects.isNull(handler)) handler = TaskUtil.requestMethodMap.get("defaultMethod");
                responseBody = handler.execute(autoTask, requestBody, temp.getCharSet(), realUrl, closeableHttpClient);
            } catch (Exception e) {
                String error = ExceptionUtils.getStackTrace(e);
                log.error("任务：{}接口请求过程中出现异常 {}", taskId, error);
                if (itf.getIntType().equals("robot") && StringUtils.isNotBlank(error) && error.contains("failed statusCode :401")) {
                    if (autoTask.getConfigs().containsKey("login")) {
                        StringRedisTemplate redisTemplate = RedisCache.getStringRedis();
                        String token = redisTemplate.opsForValue().get(SESSION_CACHE_KEY_PREFIX + itf.getComId() + ":" + autoTask.getConfigs().get("login"));
                        log.error("任务：{}接口请求过程中出现异常，移除token {}", taskId, token);
                        if (StringUtils.isNotBlank(token)) {
                            redisTemplate.delete(SESSION_CACHE_KEY_PREFIX + itf.getComId() + ":" + autoTask.getConfigs().get("login"));
                        }
                    }
                }

                //接收到请求的时间
                actionLog.setReceiveTime(new Date());

                throw e;
            }
            actionLog.setReceiveTime(new Date());
            //发送请求后响应数据处理
            responseBodyHandler(responseBody, autoTask, temp.getName(), actionLog);
        } else {
            log.info("任务:{},接口:{}执行完毕，准备回写数据！", taskId, temp.getName());
            actionLog.setRequestBody("自动");
            actionLog.setUrl("");
            actionLog.setSendTime(new Date());
        }
        try {
            //输出到模板及mongo处理
            outDataHandler(autoTask, outActionData, responseBody, actionLog, itf, temp);
        } catch (InsReturnException insException) {
            if (insException.getCode() == InsReturnException.AllowRepeat) {
                insException.setParams(outActionData);
                throw insException;
            } else {
                actionLog.setExceptionInfo(insException.getMessage());

                if (outActionData.containsKey("httpClient")) {
                    outActionData.remove("httpClient");
                }
                outActionData.remove("autoTask");

                actionLog.setOutTaskBody(JSON.toJSONString(outActionData));
                //保存请求日志
                saveActionLog(autoTask, actionLog);
                concatActionLog(autoTask, actionLog);

                HashMap<String, Object> errorInfo = autoTask.getErrorInfo();
                errorInfo.put(ErrorInfoKeys.ERROR_DESC, insException.getMessage());
                if (autoTask.getTaskEntity() instanceof Map) {
                    if (!DataUtil.containsKey((Map) autoTask.getTaskEntity(), "enquiry.errorInfo.errorcode")) {
                        if (insException.getCode() == InsReturnException.RepeatInsure) {
                            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.DUPLICATE_INSURE.getCde());
                            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
                        } else if (insException.getCode() == InsReturnException.UnableQuoteVehicle) {
                            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
                        } else if (insException.getCode() == InsReturnException.UnconsistentQuoteInfo) {
                            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNCONSISTENT_QUOTE_INFO.getCde());
                            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
                        } else if (insException.getCode() == InsReturnException.IncorrectVehicleInfo) {
                            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.INCORRECT_VEHICLE_INFO.getCde());
                            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
                        } else {
                            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                        }
                    } else {
                        errorInfo.put(ErrorInfoKeys.ERROR_CODE, DataUtil.get("enquiry.errorInfo.errorcode", autoTask.getTaskEntity()));
                        errorInfo.put(ErrorInfoKeys.ERROR_DESC, DataUtil.get("enquiry.errorInfo.errordesc", autoTask.getTaskEntity()));
                        log.info("由模板中的数据来写入errorcode:{}", errorInfo.get(ErrorInfoKeys.ERROR_DESC));
                    }
                } else if (autoTask.getTaskEntity() instanceof Enquiry) {
                    if (!errorInfo.containsKey(ErrorInfoKeys.ERROR_CODE)) {
                        fillErrorInfo(insException.getCode(), autoTask);
                    }
                }
                throw insException;
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            outActionData.remove("autoTask");
        }
        log.info("任务:{},接口:{}调用完成,访问地址:{}", taskId, temp.getName(), realUrl);
        return autoTask;
    }

    private static void responseBodyHandler(String responseBody, AutoTask autoTask, String tempName, ActionLog actionLog) throws Exception {
        if (Strings.isNullOrEmpty(responseBody)) {
            if (autoTask.getRepHeaders() != null && autoTask.getRepHeaders().size() > 0) {
                responseBody = URLDecoder.decode(JSON.toJSONString(autoTask.getRepHeaders()), "UTF-8");
                actionLog.setResponseBody(responseBody);
                autoTask.setBackRoot(DataUtil.parse(responseBody));
            } else {
                String errorMsg = String.format("任务:%s接口:%s调用返回内容为空，请联系对应保险公司接口维护人员查明原因!", autoTask.getTempValues().get("enquiryId").toString(), tempName);
                if (useVpn(autoTask)) {
                    errorMsg = String.format("任务:%s接口:%s调用返回内容为空，请检查VPN是否掉线!", autoTask.getTempValues().get("enquiryId").toString(), tempName);
                }
                throw new Exception(errorMsg);
            }

        } else {
            actionLog.setResponseBody(responseBody);
            autoTask.setBackRoot(DataUtil.parse(responseBody));
        }
    }

    private static void outDataHandler(AutoTask autoTask, Map<String, Object> outActionData, String responseBody, ActionLog actionLog, Interface itf, Template temp) throws Exception {
        if (autoTask.getTaskEntity() instanceof Map) {
            outActionData.put("enquiry", ((Map) autoTask.getTaskEntity()).get("enquiry"));
            outActionData.put("config", autoTask.getConfigs());
            outActionData.put("root", autoTask.getBackRoot());
            outActionData.put("tempValues", autoTask.getTempValues());
            if (itf.getKeepSession()) {
                outActionData.put("httpClient", autoTask.getHttpClient());
            }
            outActionData.put("autoTask", autoTask);

            ScriptUtil.engine(String.format("%s_rep", temp.getName()), itf, outActionData);

            Map<String, Object> map = new HashMap<>();
            map.put("enquiry", outActionData.get("enquiry"));
            map.put("tempValues", outActionData.get("tempValues"));

            actionLog.setOutTaskBody(JSON.toJSONString(map));
        } else {

            Map<String, Object> params = Maps.newHashMap();
            params.put("autoTask", autoTask);
            ScriptUtil.engine(String.format("%s_rep", temp.getName()), itf, params);

            outActionData.put("tempValues", autoTask.getTempValues());
            outActionData.put("enquiry", JSONObject.toJSON(autoTask.getTaskEntity()));

            actionLog.setOutTaskBody(JSON.toJSONString(outActionData));
        }
        saveActionLog(autoTask, actionLog);
        concatActionLog(autoTask, actionLog);
    }

    private static String initUrl(AutoTask autoTask, Map inActionData, Interface itf, Template temp) throws Exception {
        String realUrl = temp.getRealUrl(itf.getEnv());
        if (Strings.isNullOrEmpty(realUrl)) {
            throw new Exception(String.format("任务:%s根据环境:%s所获取到的接口:%s调用地址为空,请检查!", autoTask.getTempValues().get("enquiryId").toString(), itf.getEnv(), temp.getName()));
        } else {
            if (realUrl.contains("${")) {
                Matcher urlMatcher = urlPattern.matcher(realUrl);
                while (urlMatcher.find()) {
                    String args1 = urlMatcher.group(0);
                    String key = urlMatcher.group(1);
                    key = key.replaceAll("\\{", "");
                    key = key.replaceAll("\\}", "");
                    String value = null;
                    if (autoTask.getTempValues().containsKey(key)) {
                        value = (String) DataUtil.get(key, autoTask.getTempValues());
                    }
                    if (Strings.isNullOrEmpty(value) && autoTask.getConfigs().containsKey(key)) {
                        value = (String) DataUtil.get(key, autoTask.getConfigs());
                    }
                    if (Strings.isNullOrEmpty(value)) {
                        value = (String) DataUtil.get(key, inActionData);
                    }
                    realUrl = realUrl.replace(args1, Strings.nullToEmpty(value));
                }
            }
        }
        return realUrl;

    }

    private static String initRequestBody(AutoTask autoTask, Map<String, Object> inActionData, ActionLog actionLog, Interface itf, Template temp) throws Exception {
        String requestBody = null;
        if (autoTask.getTaskEntity() instanceof Enquiry) {

            //就是为了日志记录，执行时只有 autoTask。autoTask 中 HttpClient 无法序列化
            inActionData.put("enquiry", autoTask.getTaskEntity());
            inActionData.put("script", "");
            inActionData.put("taskType", autoTask.getTaskType());
            inActionData.put("config", autoTask.getConfigs());
            inActionData.put("params", autoTask.getParams());
            inActionData.put("reqHeaders", autoTask.getReqHeaders());
            inActionData.put("tempValues", autoTask.getTempValues());

            //记录日志
            String inTaskBody = JSON.toJSONString(inActionData);
            actionLog.setInTaskBody(inTaskBody);

            Map<String, Object> params = Maps.newHashMap();
            params.put("autoTask", autoTask);
            //构建请求报文
            requestBody = ScriptUtil.engine(temp.getName(), itf, params);
        } else if (autoTask.getTaskEntity() instanceof Map) {
            //engine入参
            inActionData.put("enquiry", ((Map) autoTask.getTaskEntity()).get("enquiry"));
            inActionData.put("script", "");
            inActionData.put("taskType", autoTask.getTaskType());
            inActionData.put("config", autoTask.getConfigs());
            inActionData.put("postParameters", autoTask.getParams());
            inActionData.put("getParameters", autoTask.getParams());
            inActionData.put("reqHeaders", autoTask.getReqHeaders());
            inActionData.put("repHeaders", autoTask.getRepHeaders());
            inActionData.put("tempValues", autoTask.getTempValues());

            //记录日志
            String inTaskBody = JSON.toJSONString(inActionData);
            actionLog.setInTaskBody(inTaskBody);

            if (itf.getKeepSession()) {
                inActionData.put("httpClient", autoTask.getHttpClient());
            }

            inActionData.put("autoTask", autoTask);

            requestBody = ScriptUtil.engine(temp.getName(), itf, inActionData);
        }

        if (autoTask.getConfigs().containsKey("headers")) {
            Object headersObj = autoTask.getConfigs().get("headers");
            if (headersObj != null) {
                if (headersObj instanceof Map headers) {
                    //补充 head 配置
                    autoTask.getReqHeaders().putAll(headers);
                } else if (headersObj instanceof String headers) {
                    //重置 head 配置
                    autoTask.setReqHeaders(JSON.parseObject(headers, Map.class));
                }
            }
        }

        //2是 webdriver，以下都是为了记录日志
        if (!"2".equals(autoTask.getConfigs().get("robotType"))) {
            actionLog.setRequestBody(buildRequestBody4Log(autoTask, requestBody));
        }

        return requestBody;
    }

    private static String buildRequestBody4Log(AutoTask autoTask, String requestBody) {
        if (StrUtil.isBlank(requestBody)) {
            //form 请求
            Map<String, Object> params = autoTask.getParams();
            List arrayParams = autoTask.getArrayParams();

            if (CollUtil.isEmpty(params) && CollUtil.isEmpty(arrayParams)) {
                return "无";
            } else {
                //form 日志
                Map<String, Object> map = new HashMap<>();
                map.put("headers", autoTask.getReqHeaders());
                map.put("params", CollUtil.isEmpty(params) ? arrayParams : params);

                return JSON.toJSONString(map);
            }
        }

        return requestBody;
    }

    private static void concatActionLog(AutoTask autoTask, ActionLog actionLog) {
        if (OldTableSwitch.isUseOldTable()) {
            concatMongoActionLog(autoTask, actionLog);
        }
        concatHBaseActionLog(autoTask, actionLog);
    }

    private static void concatMongoActionLog(AutoTask autoTask, ActionLog actionLog) {
        if (Strings.isNullOrEmpty(autoTask.getActionLogMongo())) {
            if (Strings.isNullOrEmpty(autoTask.getActionLogsMongo())) {
                autoTask.setActionLogsMongo("null@".concat(actionLog.getActionName()));
            } else {
                autoTask.setActionLogsMongo(autoTask.getActionLogsMongo().concat(",null@").concat(actionLog.getActionName()));
            }
        } else {
            if (Strings.isNullOrEmpty(autoTask.getActionLogsMongo())) {
                autoTask.setActionLogsMongo(autoTask.getActionLogMongo().concat("@").concat(actionLog.getActionName()));
            } else {
                autoTask.setActionLogsMongo(autoTask.getActionLogsMongo().concat(",").concat(autoTask.getActionLogMongo().concat("@").concat(actionLog.getActionName())));
            }
        }
    }

    private static void concatHBaseActionLog(AutoTask autoTask, ActionLog actionLog) {
        String rowKey = actionLog.generateRowKey();
        if (Strings.isNullOrEmpty(autoTask.getActionLogs())) {
            autoTask.setActionLogs(rowKey.concat("@").concat(actionLog.getActionName()));
        } else {
            autoTask.setActionLogs(autoTask.getActionLogs().concat(",").concat(rowKey.concat("@").concat(actionLog.getActionName())));
        }
    }

    private static void fillErrorInfo(int errorCode, AutoTask autoTask) {
        HashMap<String, Object> errorInfo = autoTask.getErrorInfo();
        if (errorCode == InsReturnException.RepeatInsure) {
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.DUPLICATE_INSURE.getCde());
            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
            if ("true".equals(autoTask.getTempValues().get("isReserved"))) {
                PlatformUtil.callBackErrorMsg(autoTask);
            }
        } else if (errorCode == InsReturnException.UnableQuoteVehicle) {
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
        } else if (errorCode == InsReturnException.UnconsistentQuoteInfo) {
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNCONSISTENT_QUOTE_INFO.getCde());
            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
        } else if (errorCode == InsReturnException.IncorrectVehicleInfo) {
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.HTTP_REQ.getCde());
            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
        } else if (errorCode == 16) {
            //暂时处理,投保单查询失败，返回16终止轮询
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, 16);
            errorInfo.put(ErrorInfoKeys.ERROR_STOP, true);
        } else {
            errorInfo.put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
        }
    }

    private static void saveActionLog(AutoTask task, ActionLog actionLog) {
        if (task != null && actionLog != null) {
            //通过sendTime创建TTL索引，定时删除数据，防止sendTime存在空值
            if (actionLog.getSendTime() == null) {
                actionLog.setSendTime(new Date());
            }
            if (OldTableSwitch.isUseOldTable()) {
                saveHBaseActionLog(task, actionLog);
                saveMongoActionLog(task, actionLog);
            } else {
                saveHBaseActionLog(task, actionLog);
            }
        }
    }

    private static void saveHBaseActionLog(AutoTask task, ActionLog actionLog) {
        try {
            if (Strings.isNullOrEmpty(actionLog.getActionId())) {
                actionLog.setActionId(UUID.randomUUID().toString(true));
            }
            hbaseActionLogRepository.save(actionLog);
        } catch (Exception e) {
            String taskId = task.getTraceKey().concat("@").concat(task.getCompanyId());
            log.error("任务:{},模板:{}保存hbase日志失败，错误信息:{}", taskId, actionLog.getActionName(), ExceptionUtils.getStackTrace(e));
        }
    }

    private static void saveMongoActionLog(AutoTask task, ActionLog actionLog) {
        com.cheche365.bc.entity.mongo.ActionLog actionLogMongo = new com.cheche365.bc.entity.mongo.ActionLog();
        BeanUtil.copyProperties(actionLog, actionLogMongo);
        try {
            mongoRepository.save(actionLogMongo);
            task.setActionLogMongo(actionLogMongo.get_id());
        } catch (Exception e) {
            String taskId = task.getTraceKey().concat("@").concat(task.getCompanyId());
            log.error("任务:{},模板:{}保存mongo日志失败，错误信息:{}", taskId, actionLog.getActionName(), ExceptionUtils.getStackTrace(e));
        }
    }


    /**
     * 任务过单个接口后的结果
     *
     * @param inter 单个接口
     * @return 任务处理完成后的结果
     */

    public static AutoTask process(Interface inter, AutoTask autoTask) throws Exception {
        if (inter == null) {
            return autoTask;
        }
        String taskId = autoTask.getTempValues().get("enquiryId").toString();
        ActionLog actionLog;
        int execedTimes = 0;
        for (int i = 0; i < inter.getTemplates().size(); ) {
            actionLog = new ActionLog();
            Template temp = inter.getTemplates().get(i);
            autoTask.setCurrentStep(i);
            try {
                autoTask = process(inter, temp, autoTask, actionLog);
                if (autoTask.getTaskEntity() instanceof Map) {
                    Map dataSource = (Map) autoTask.getTaskEntity();
                    Object taxCharge = DataUtil.get("enquiry.baseSuiteInfo.taxSuiteInfo.charge", dataSource);
                    Object jgVehicleType = DataUtil.get("enquiry.carInfo.jgVehicleType", dataSource);
                    if (jgVehicleType != null && (int) jgVehicleType != 21 && taxCharge != null) {
                        if (taxCharge instanceof BigDecimal && ((BigDecimal) taxCharge).compareTo(new BigDecimal(0)) == 0) {
                            //判断是否完税车
                            MapUtil.putMap(dataSource, "enquiry/carInfo/paidtaxes", 1);
                        } else if (taxCharge instanceof Double && ((double) taxCharge) == 0) {
                            //判断是否完税车
                            MapUtil.putMap(dataSource, "enquiry/carInfo/paidtaxes", 1);
                        }
                    }
                } else if (autoTask.getTaskEntity() instanceof Enquiry) {
                    Enquiry enquiry = (Enquiry) autoTask.getTaskEntity();
                    if (enquiry != null && enquiry.getOrder() != null) {
                        if (enquiry.getOrder().getCarInfo() != null) {
                            Integer jgVehicleType = enquiry.getOrder().getCarInfo().getJgVehicleType();
                            if (enquiry.getOrder().getSuiteInfo() != null && enquiry.getOrder().getSuiteInfo().getTaxSuiteInfo() != null) {
                                if (jgVehicleType != null && jgVehicleType != 21 && (enquiry.getOrder().getSuiteInfo().getTaxSuiteInfo().getDiscountCharge() == null || enquiry.getOrder().getSuiteInfo().getTaxSuiteInfo().getDiscountCharge().compareTo(new BigDecimal(0)) == 0)) {
                                    //完税
                                    enquiry.getOrder().getCarInfo().setPaidtaxes(1);
                                }
                            }
                        }
                    }
                }
            } catch (InsReturnException insRetE) {
                if (insRetE.getCode() == InsReturnException.AllowRepeat) {
                    SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
                    filter.getExcludes().add("httpClient");
                    //重试次数从11次增加为15次
                    if (execedTimes > 14) {
                        log.warn("任务:{}重试执行了15次依然不成功!放弃执行!", taskId);
                        BWException parseException = new BWException(insRetE);
                        autoTask.getErrorInfo().put("errordesc", insRetE.getMessage());
                        autoTask.getErrorInfo().put("errorcode", ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                        parseException.setReturnValue(autoTask);
                        actionLog.setExceptionInfo(insRetE.getMessage());
                        actionLog.setOutTaskBody(JSONObject.toJSONString(insRetE.getParams(), filter));
                        saveActionLog(autoTask, actionLog);
                        concatActionLog(autoTask, actionLog);
                        throw parseException;
                    }
                    i = i - insRetE.getStep() + 1;
                    if (i < 0) {
                        BWException parseException = new BWException(insRetE);
                        autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_DESC, "重复模版执行异常：回退模版数超过可执行模版数");
                        autoTask.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde());
                        parseException.setReturnValue(autoTask);
                        actionLog.setExceptionInfo("重复模版执行异常：返回步骤数超过可执行模版数");
                        actionLog.setOutTaskBody("");

                        saveActionLog(autoTask, actionLog);
                        concatActionLog(autoTask, actionLog);

                        throw parseException;
                    } else {
                        log.warn("任务:{} 数据经调整后模板:{}将进行第{}次重试，调整原因:{}", taskId, temp.getName(), ++execedTimes, insRetE.getMessage());
                        actionLog.setExceptionInfo(insRetE.getMessage());
                        actionLog.setOutTaskBody(JSONObject.toJSONString(insRetE.getParams(), filter));
                        saveActionLog(autoTask, actionLog);
                        concatActionLog(autoTask, actionLog);
                        autoTask.getParams().clear();
                        autoTask.getReqHeaders().clear();
                        continue;
                    }
                } else {
                    log.error("无法处理异常:", insRetE);
                    throw insRetE;
                }
            } catch (Exception e) {
                log.error("任务:{},执行异常：{}", autoTask.getTempValues().get("enquiryId"), ExceptionUtils.getStackTrace(e));
                if (inter.getComId().startsWith("2005") && autoTask.getTempValues().containsKey("isPaymentDept")) {
                    //人保承保查询
                    autoTask.getErrorInfo().put("errordesc", e.getMessage());
                    autoTask.getErrorInfo().put("errorcode", ExceptionCde.APPROVED_QUERY_PAY_SURE_FAIL.getCde());
                    autoTask.getErrorInfo().put("excepNotiPhoneNum", autoTask.getConfigs().get("excepNotiPhoneNum"));
                }
                actionLog.setExceptionInfo(Strings.isNullOrEmpty(e.getMessage()) ? e.toString() : e.getMessage());
                actionLog.setOutTaskBody("");
                saveActionLog(autoTask, actionLog);
                concatActionLog(autoTask, actionLog);
                throw e;
            }
            autoTask.getParams().clear();
            autoTask.getReqHeaders().clear();
            i = autoTask.getCurrentStep() + 1;
            if (autoTask.isEndFlag()) {
                break;
            }
        }
        return autoTask;
    }

    public static void disposeFailed(AutoTask t, Throwable ex, ActorRef actorRef) {
        final String cause = Strings.isNullOrEmpty(ex.getMessage()) ? ex.toString() : ex.getMessage();
        t.setResultStr(cause);
        t.setEndFlag(true);
        t.setResultFlag(false);
        Map<String, Object> errorInfo = t.getErrorInfo();
        if (t.getCompanyId().startsWith("2005") && t.getTempValues().containsKey("isPaymentDept")) {
            //人保承保查询
            errorInfo.putAll(ErrorInfoUtil.build(ExceptionCde.APPROVED_QUERY_PAY_SURE_FAIL.getCde(), cause));
            errorInfo.put("excepNotiPhoneNum", t.getConfigs().get("excepNotiPhoneNum"));
        } else {
            errorInfo.putAll(ErrorInfoUtil.build(ExceptionCde.UNABLE_QUOTE_VEHICLE.getCde(), String.format("任务执行异常:%s", cause)));
        }
        t.setTaskStatus(TaskStatus.getFailedState(t.getTaskTypeName()));
        actorRef.tell(t, ActorRef.noSender());
    }

    public static void updateScriptCache(String taskType) {
        staticInterfaceService.updateScriptCache(taskType);
    }

    public static void killWinActors(String actorName) {
        List<String> actorKeys = BaseActor.allActors.keySet().stream().filter(key -> key.startsWith(actorName)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(actorKeys)) {
            for (String actorKey : actorKeys) {
                ActorSelection actor = staticActorSystem.actorSelection(BaseActor.allActors.get(actorKey));
                actor.tell(PoisonPill.getInstance(), ActorRef.noSender());
                BaseActor.allActors.remove(actorKey);
            }
            log.info("akka actor关闭成功!");
        } else {
            log.info("akka actor不存在!");
        }
    }

    /**
     * 更新平台信息码表
     *
     * @param message
     */
    public static void updatePlatformInfoCodeCache(String message) {
        staticBcPlatforminfoCodeService.updateCache(message);
    }

    public static void populateTransTaskErrorMsg(TransTaskMsg transTaskMsg, Exception e) {
        transTaskMsg.getAutoTask().setResultStr(e.getMessage());
        transTaskMsg.getAutoTask().setFailureCause(e.getMessage());
    }

    public static void loadConfig(Interface ift, AutoTask task) throws Exception {
        Map enquiry;
        if (task.getTaskEntity() instanceof Enquiry) {
            enquiry = JSONObject.parseObject(task.getApplyJson());
            if (enquiry == null || enquiry.get("flag") == null) {
                return;
            }
        } else {
            enquiry = (Map) task.getTaskEntity();
            if (cn.hutool.core.map.MapUtil.isEmpty(task.getConfigs()) && DataUtil.containsKey((Map) task.getTaskEntity(), "enquiry.configInfo.configMap")) {
                task.setConfigs((Map<String, Object>) DataUtil.get("enquiry.configInfo.configMap", task.getTaskEntity()));
            }
        }

        Map orgMap = getConfigs(ift, task, enquiry);
        task.setConfigs(orgMap);

        if (cn.hutool.core.map.MapUtil.isEmpty(orgMap) && task.getTraceKey() != null) {
            log.warn("任务:{}未能加载到任务配置信息，请核实确认!", task.getTraceKey().concat("@").concat(task.getCompanyId()));
            throw new TaskException(task, "未能加载到任务配置信息");
        }

        //载入 headers
        if (orgMap != null && orgMap.containsKey("headers")) {
            String headersStr = (String) orgMap.get("headers");
            Map headers = JSON.parseObject(headersStr, Map.class);
            task.setReqHeaders(headers);
        }
    }

    private static Map getConfigs(Interface ift, AutoTask task, Map enquiry) {
        Map orgMap;
        if (ift.getUseTaskConfig()) {
            orgMap = task.getConfigs();
        } else if (enquiry != null) {
            orgMap = getOrgMap(ift, enquiry);
        } else {
            orgMap = ift.loadConfig();
        }
        return orgMap;
    }

    private static Map getOrgMap(Interface ift, Map enquiry) {
        Map orgMaps = ift.loadConfig();

        Map orgMap;
        //TODO singleSite->orgCode->default
        String singleSite = (String) enquiry.getOrDefault("singleSite", null);
        String orgCode = getOrgCode(enquiry, singleSite);

        if (Strings.isNullOrEmpty(singleSite)) {
            if (orgMaps.containsKey(orgCode)) {
                orgMap = (Map) orgMaps.get(orgCode);
            } else {
                orgMap = (Map) orgMaps.get("default");
            }
        } else {
            orgMap = (Map) orgMaps.get(singleSite);
            if (orgMap == null) {
                orgMap = (Map) orgMaps.get(orgCode);
            }
        }
        return orgMap;
    }

    private static String getOrgCode(Map enquiry, String singleSite) {
        String orgCode = (String) enquiry.getOrDefault("orgCode", null);
        if (Strings.isNullOrEmpty(orgCode) && !Strings.isNullOrEmpty(singleSite)) {
            orgCode = singleSite.substring(0, 4).concat("000000");
        }
        return orgCode;
    }


    public static Map<String, Object> generateCallbackHttpEntity(AutoTask task) throws Exception {
        Map<String, Object> tempValues = task.getTempValues();
        /*构建回写数据结构*/
        Map<String, Object> feedbackBody = Maps.newHashMap();

        feedbackBody.put("taskId", task.getTaskId());
        feedbackBody.put("taskType", task.getTaskTypeName());
        feedbackBody.put("msg", task.getErrorMsg());

        mapTempValues(feedbackBody, tempValues);

        //返回重复投保标志
        if (Objects.nonNull(tempValues.get("repeatInsure"))) {
            feedbackBody.put("repeatInsure", tempValues.get("repeatInsure"));
        }

        Map execteParseOut = DataUtil.parseOutEdiInterface((Map) task.getTaskEntity());
        Map<String, Object> platformInfo = (Map<String, Object>) tempValues.get("platformInfo");
        Map<String, Object> definition = null;
        if (Objects.nonNull(platformInfo)) {
            definition = (Map<String, Object>) platformInfo.get("definition");
        }
        //常规报价核保任务与其他任务回写字段有别
        if (!task.getTaskType().startsWith("other")) {
            //成功的状态不回写错误信息
            if (!TaskStatus.SUCCESS_CALLBACK_STATUS.contains(task.getTaskStatus())) {
                if (cn.hutool.core.map.MapUtil.isNotEmpty(task.getErrorInfo())) {
                    if (Objects.nonNull(task.getErrorInfo().get(ErrorInfoKeys.ERROR_DESC))) {
                        String errorDesc = task.getErrorInfo().get(ErrorInfoKeys.ERROR_DESC).toString();
                        if (Objects.isNull(feedbackBody.get("repeatInsure"))
                            && (errorDesc.contains("重复保单")
                            || errorDesc.contains("重复投保")
                            || errorDesc.contains("规定时间范围")
                            || errorDesc.contains("晚于当前日期")
                            || errorDesc.contains("不能早于起保日期"))) {
                            //如果模板没有处理重复投保，框架根据错误信息给出重复投保标志
                            feedbackBody.put("repeatInsure", true);
                            task.getErrorInfo().put(ErrorInfoKeys.ERROR_CODE, ExceptionCde.DUPLICATE_INSURE.getCde());
                        }
                    }
                    feedbackBody.put(ErrorInfoKeys.ERROR_INFO, task.getErrorInfo());
                    if (MapUtils.getBoolean(task.getConfigs(), "backSq", false)) {
                        feedbackBody.put("sq", execteParseOut.get("sq"));
                    }
                } else {
                    feedbackBody.put(ErrorInfoKeys.ERROR_INFO, execteParseOut.get(ErrorInfoKeys.ERROR_INFO));
                    feedbackBody.put("sq", execteParseOut.get("sq"));
                }
            } else {
                if (execteParseOut != null) {
                    feedbackBody.putAll(execteParseOut);
                    if (cn.hutool.core.map.MapUtil.isNotEmpty(task.getErrorInfo())) {
                        feedbackBody.put(ErrorInfoKeys.ERROR_INFO, task.getErrorInfo());
                    } else {
                        feedbackBody.put(ErrorInfoKeys.ERROR_INFO, execteParseOut.get(ErrorInfoKeys.ERROR_INFO));
                    }
                }
            }
        } else {
            JSONObject applyData = null;
            try {
                applyData = JSONObject.parseObject(task.getApplyJson());
            } catch (Exception ignored) {
            }
            if (applyData != null) {
                feedbackBody.putAll(applyData);
            }
            if (execteParseOut != null) {
                feedbackBody.putAll(execteParseOut);
            }
        }
        if (!Strings.isNullOrEmpty(task.getFeedbackJson())) {
            feedbackBody.putAll(JSON.parseObject(task.getFeedbackJson(), Map.class));
        }
        if (!feedbackBody.containsKey("taskStatus")) {
            feedbackBody.put("taskStatus", task.getTaskStatus());
        }
        Map<String, String> codeCacheMap = BcPlatforminfoCodeServiceImpl.cacheMap;
        // otherInfo 回写
        JSONArray otherInfos = new JSONArray();
        if (Objects.nonNull(definition)) {
            if (cn.hutool.core.map.MapUtil.isNotEmpty(codeCacheMap)) {
                definition.forEach((key, value) -> {
                    if (codeCacheMap.containsKey(key)) {
                        JSONObject otherInfo = new JSONObject();
                        otherInfo.put("code", key);
                        otherInfo.put("label", codeCacheMap.get(key));
                        otherInfo.put("value", value);
                        otherInfos.add(otherInfo);
                    }
                });
            }
        }

        if (CollectionUtils.isNotEmpty(otherInfos)) {
            feedbackBody.put("otherInfo", otherInfos);
        }

        return feedbackBody;
    }

    public static void mapTempValues(Map<String, Object> feedbackBody, Map<String, Object> tempValues) {
        feedbackBody.put("businessId", tempValues.get("businessId"));
        feedbackBody.put("buybusitype", tempValues.get("buybusitype"));
        feedbackBody.put("monitorid", tempValues.get("monitorid"));
        feedbackBody.put("processType", tempValues.get("processType"));
        feedbackBody.put("enquiryId", tempValues.get("enquiryId"));
    }

}
