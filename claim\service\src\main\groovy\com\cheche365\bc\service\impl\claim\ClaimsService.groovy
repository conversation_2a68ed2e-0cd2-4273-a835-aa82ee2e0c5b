package com.cheche365.bc.service.impl.claim

import com.cheche365.bc.entity.AutoTask
import com.cheche365.bc.entity.mongo.CallBackLog
import com.cheche365.bc.exception.FlowException
import com.cheche365.bc.service.dto.ClaimsReq
import com.cheche365.bc.service.impl.LinkHandleService
import com.cheche365.bc.service.impl.claim.base.FlowRunner
import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import groovy.util.logging.Slf4j
import jakarta.annotation.Resource
import org.apache.commons.lang.exception.ExceptionUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.stereotype.Service

import java.time.LocalDateTime



/**
 * 理赔服务
 */
@Slf4j
@Service
class ClaimsService implements FlowRunner {

    @Resource
    private LinkHandleService linkHandleService

    @Value('${router.script.dir}')
    private String scriptDir

    def preHandler(ClaimsReq request) {
        if ('2026' == request.companyId && 'uploadImages' == request.taskType) {     //影像上传时，长链接转成短链接返给模板
            request.claims.imagesReq?.each {
                if (it?.url) {
                    it['originalUrl'] = it.url
                    it.url = linkHandleService.genShortLink(it.url as String)
                }
            }
        }
    }

    Map<String, Object> execute(ClaimsReq request) {

        preHandler(request)

        def autoTask = createAutoTask(request)

        //查询任务优先读库 查询保司回调记录
        def callbackInfo = queryCallback(request, autoTask)
        if (callbackInfo) {
            return callbackInfo
        }
        addAutoTask(autoTask)
        def flow = buildFlow(
            request.taskType,
            request.companyId,
            request.processType,
            autoTask.autoTraceId,
            request.claims
        )
        def flowCopy = flow.flow.collect { item -> item }
        def flowResult = [:]
        try {
            flowResult = runFlow(flow.flow, request.companyId, flowCopy, flow.context)
            log.info("flowResult：{}", flowResult)
        } catch (FlowException ex) {
            log.error 'flow执行（{}）失败：{}', autoTask.autoTraceId, ExceptionUtils.getStackTrace(ex)
            updateAutoTaskError('', ex.getMessage(), autoTask.autoTraceId)
            throw ex
        } finally {
            updateAutoTask(flow.context.flowLog[[0..-2]] - 'null', JsonOutput.toJson(flowResult), autoTask.autoTraceId)
        }
        flowResult
    }

    private static LinkedHashMap<String, Object> createAutoTask(ClaimsReq request) {
        [
            'autoTraceId'         : UUID.randomUUID().toString(),
            'actionLogs'          : '',
            'applyJson'           : JsonOutput.toJson(request.claims),
            'endTime'             : LocalDateTime.now(),
            'companyId'                : request.companyId,
            'plateNo'                : request.claims.claimsReq?.auto?.licensePlateNo,
            'resultStr'           : '',
            'startTime'           : LocalDateTime.now(),
            'taskStatus'          : '',
            'taskType'            : request.processType + '-' + request.companyId + '-' + 'claims-' + request.taskType,
            'traceKey'            : request.claims?.enquiryId?.split('@')[0],
            'feedbackJson'        : '',
            'bizProposeNo'        : '',
            'efcProposeNo'        : '',
            'bizPolicyNo'         : '',
            'efcPolicyNo'         : '',
            'failureCause'        : '',
            'failureCauseCategory': '',
            'internet_sales'      : 0,
            'vin'                 : request.claims.claimsReq?.auto?.vinNo,
            'source_kind'         : null,
            'data_source_log_id'  : null
        ]
    }

    /**
     * 理赔过程中，保司会回调车车，当回调车车把数据存放在mongo，查询的时候，优先从mongo中获取，获取后直接返回
     * @param taskType
     * @param claims
     * @return
     */
    Map<String, Object> queryCallback(ClaimsReq request, def autoTask) {
        if (request.taskType != 'query') {
            return
        }

        def callbackInfo = mongoTemplate.findOne(new Query(Criteria.where('claimsNo').is(request.claims?.claimsNo as String)), CallBackLog.class)
        if (!callbackInfo) {
            return
        }

        autoTask.applyJson = callbackInfo?.callbackInfo
        autoTask.feedbackJson = callbackInfo?.callbackResult
        addAutoTask(autoTask)
        return new JsonSlurper().parseText(callbackInfo.callbackResult as String) as Map<String, Object>
    }
    /**
     * 添加autoTask到数据库中
     * @param jdbcTemplate
     * @param autoTask 百川任务记录表，历史遗留表，表字段说明如下：
     *   字段名  字段类型  字段描述  备注
     autoTraceId  varchar  任务唯一id  uuid
     actionLogs  text  任务执行动作即模板记录
     applyJson  text  任务原始数据  json
     endTime  datetime  任务执行结束时间  到秒级别
     companyId  varchar  任务目标保司编号
     plateNo  varchar  任务车牌号
     resultStr  text  任务执行结果文字描述
     startTime  datetime  任务接收时间
     taskStatus  varchar  任务执行结果状态
     taskType  varchar  任务类型  edi-2007-approvedquery
     feedbackJson  text  任务回写数据  json
     bizProposeNo  varchar  商业险投保单号
     efcProposeNo  varchar  交强险投保单号
     bizPolicyNo  varchar  商业险保单号
     efcPolicyNo  varchar  交强险保单号
     failureCause  varchar  任务失败错误信息
     failureCauseCategory  varchar  任务失败错误信息翻译
     internet_sales  int  任务是否网销任务
     * @return
     */
    void addAutoTask(autoTask) {
        try {
            taskService.save(autoTask)
        } catch (ex) {
            log.warn 'addAutoTask（{}）失败：{}', autoTask, ex.message
            throw ex
        }
    }

    /**
     * 更新autoTask
     * @param jdbcTemplate
     * @param autoTask
     * @return
     */
    void updateAutoTask(actionLogs, feedbackJson, autoTraceId) {

        try {
            AutoTask autoTask = new AutoTask()
            autoTask.setActionLogs(actionLogs as String)
            autoTask.setResultStr('此操作为同步操作，无需回写。为适应百川任务状态显示：回写成功！')
            autoTask.setFeedbackJson(feedbackJson as String)
            autoTask.setAutoTraceId(autoTraceId as String)
            autoTask.setEndTime(LocalDateTime.now())
            taskService.updateClaimInfoByAutoTraceId(autoTask)
        } catch (ex) {
            log.error 'updateAutoTask（{}）失败：{}', autoTraceId, ex.message
            throw ex
        }
    }

    /**
     * 更新autoTask
     * @param jdbcTemplate
     * @param autoTask
     * @return
     */
    void updateAutoTaskError(taskStatus, resultStr, autoTraceId) {
        try {
            AutoTask autoTask = new AutoTask()
            autoTask.setAutoTraceId(autoTraceId as String)
            autoTask.setTaskStatus(taskStatus as String)
            autoTask.setResultStr(resultStr as String)
            taskService.updateClaimStatusByAutoTraceId(autoTask)
        } catch (ex) {
            log.error 'updateAutoTaskError（{}）失败：{}', autoTraceId, ExceptionUtils.getStackTrace(ex)
            throw ex
        }
    }
}

